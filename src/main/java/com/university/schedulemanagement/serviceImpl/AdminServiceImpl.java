package com.university.schedulemanagement.serviceImpl;

import com.university.schedulemanagement.dto.response.DashboardResponse;
import com.university.schedulemanagement.repository.*;
import com.university.schedulemanagement.service.AdminService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * AdminServiceImpl - Implementation của AdminService
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class AdminServiceImpl implements AdminService {

    private final CanBoRepository canBoRepository;
    private final LichGiangRepository lichGiangRepository;
    private final MonHocRepository monHocRepository;
    private final LopHocRepository lopHocRepository;
    private final PhongHocRepository phongHocRepository;
    private final PBMMRepository pbmmRepository;
    private final NienKhoaRepository nienKhoaRepository;
    private final HocKyRepository hocKyRepository;

    @Override
    @Transactional(readOnly = true)
    public DashboardResponse getDashboardData() {
        log.info("Getting dashboard data for admin");

        DashboardResponse dashboard = new DashboardResponse();
        
        // Thống kê tổng quan
        dashboard.setTotalTeachers(canBoRepository.countByTrangThaiTrue());
        dashboard.setTotalSchedules(lichGiangRepository.countByTrangThaiTrue());
        dashboard.setTotalSubjects(monHocRepository.countByTrangThaiTrue());
        dashboard.setTotalClasses(lopHocRepository.countByTrangThaiTrue());
        dashboard.setTotalRooms(phongHocRepository.countByTrangThaiTrue());
        dashboard.setTotalDepartments(pbmmRepository.countByTrangThaiTrue());

        // Thống kê theo thời gian
        dashboard.setTotalAcademicYears(nienKhoaRepository.count());
        dashboard.setTotalSemesters(hocKyRepository.count());
        
        // Học kỳ hiện tại
        hocKyRepository.findByHienTaiTrue().ifPresent(hocKy -> {
            dashboard.setCurrentSemester(hocKy.getTenHocKy());
            dashboard.setCurrentSemesterId(hocKy.getIdHocKy());
        });

        // Thống kê lịch giảng trong tuần
        dashboard.setWeeklySchedules(lichGiangRepository.countSchedulesThisWeek());
        
        // Thống kê giờ giảng
        dashboard.setTotalTeachingHours(lichGiangRepository.sumTotalTeachingHours());

        log.info("Dashboard data retrieved successfully");
        return dashboard;
    }

    @Override
    @Transactional(readOnly = true)
    public Object getSystemInfo() {
        log.info("Getting system information");

        Map<String, Object> systemInfo = new HashMap<>();
        
        // Thông tin hệ thống
        systemInfo.put("systemName", "Hệ Thống Quản Lý Lịch Giảng");
        systemInfo.put("version", "1.0.0");
        systemInfo.put("buildDate", "2024-01-01");
        systemInfo.put("environment", "Production");
        
        // Thông tin database
        systemInfo.put("databaseType", "MySQL");
        systemInfo.put("databaseVersion", "8.0");
        
        // Thống kê dữ liệu
        Map<String, Long> dataStats = new HashMap<>();
        dataStats.put("totalUsers", canBoRepository.count());
        dataStats.put("totalSchedules", lichGiangRepository.count());
        dataStats.put("totalSubjects", monHocRepository.count());
        dataStats.put("totalClasses", lopHocRepository.count());
        dataStats.put("totalRooms", phongHocRepository.count());
        dataStats.put("totalDepartments", pbmmRepository.count());
        systemInfo.put("dataStatistics", dataStats);
        
        // Thông tin thời gian
        systemInfo.put("serverTime", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        systemInfo.put("uptime", "System uptime information");
        
        return systemInfo;
    }

    @Override
    public void initializeSystemData() {
        log.info("Initializing system data");
        
        try {
            // Logic khởi tạo dữ liệu mẫu
            // Có thể gọi DataInitializer hoặc tạo dữ liệu mẫu mới
            
            log.info("System data initialization completed");
        } catch (Exception e) {
            log.error("Error initializing system data: {}", e.getMessage());
            throw new RuntimeException("Lỗi khi khởi tạo dữ liệu hệ thống", e);
        }
    }

    @Override
    public String createBackup() {
        log.info("Creating system backup");
        
        try {
            // Logic tạo backup
            String backupFileName = "backup_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".sql";
            String backupPath = "/backups/" + backupFileName;
            
            // Thực hiện backup database
            // mysqldump hoặc sử dụng Spring Boot Actuator
            
            log.info("Backup created successfully: {}", backupPath);
            return backupPath;
        } catch (Exception e) {
            log.error("Error creating backup: {}", e.getMessage());
            throw new RuntimeException("Lỗi khi tạo backup", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Object getSystemStatistics() {
        log.info("Getting system statistics");

        Map<String, Object> statistics = new HashMap<>();
        
        // Thống kê người dùng
        Map<String, Long> userStats = new HashMap<>();
        userStats.put("totalUsers", canBoRepository.count());
        userStats.put("activeUsers", canBoRepository.countByTrangThaiTrue());
        userStats.put("adminUsers", canBoRepository.countByVaiTroTenVaiTro("Admin"));
        userStats.put("teacherUsers", canBoRepository.countByVaiTroTenVaiTro("Giảng viên"));
        statistics.put("userStatistics", userStats);
        
        // Thống kê lịch giảng
        Map<String, Long> scheduleStats = new HashMap<>();
        scheduleStats.put("totalSchedules", lichGiangRepository.count());
        scheduleStats.put("activeSchedules", lichGiangRepository.countByTrangThaiTrue());
        scheduleStats.put("thisWeekSchedules", lichGiangRepository.countSchedulesThisWeek());
        scheduleStats.put("thisMonthSchedules", lichGiangRepository.countSchedulesThisMonth());
        statistics.put("scheduleStatistics", scheduleStats);
        
        // Thống kê môn học và lớp học
        Map<String, Long> academicStats = new HashMap<>();
        academicStats.put("totalSubjects", monHocRepository.count());
        academicStats.put("totalClasses", lopHocRepository.count());
        academicStats.put("totalDepartments", pbmmRepository.count());
        academicStats.put("totalRooms", phongHocRepository.count());
        statistics.put("academicStatistics", academicStats);
        
        return statistics;
    }

    @Override
    @Transactional(readOnly = true)
    public Object checkSystemHealth() {
        log.info("Checking system health");

        Map<String, Object> health = new HashMap<>();
        
        try {
            // Kiểm tra database connection
            long userCount = canBoRepository.count();
            health.put("database", "UP");
            health.put("databaseRecords", userCount);
            
            // Kiểm tra memory usage
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            Map<String, Long> memoryInfo = new HashMap<>();
            memoryInfo.put("total", totalMemory);
            memoryInfo.put("used", usedMemory);
            memoryInfo.put("free", freeMemory);
            health.put("memory", memoryInfo);
            
            health.put("status", "HEALTHY");
            health.put("timestamp", LocalDateTime.now());
            
        } catch (Exception e) {
            health.put("status", "UNHEALTHY");
            health.put("error", e.getMessage());
            log.error("System health check failed: {}", e.getMessage());
        }
        
        return health;
    }

    @Override
    public void cleanupOldData() {
        log.info("Cleaning up old data");
        
        try {
            // Logic dọn dẹp dữ liệu cũ
            // Xóa các bản ghi cũ, logs, temporary files, etc.
            
            log.info("Old data cleanup completed");
        } catch (Exception e) {
            log.error("Error cleaning up old data: {}", e.getMessage());
            throw new RuntimeException("Lỗi khi dọn dẹp dữ liệu cũ", e);
        }
    }

    @Override
    public void optimizeDatabase() {
        log.info("Optimizing database");
        
        try {
            // Logic tối ưu hóa database
            // OPTIMIZE TABLE, ANALYZE TABLE, etc.
            
            log.info("Database optimization completed");
        } catch (Exception e) {
            log.error("Error optimizing database: {}", e.getMessage());
            throw new RuntimeException("Lỗi khi tối ưu hóa database", e);
        }
    }
}
