package com.university.schedulemanagement.serviceImpl;

import com.university.schedulemanagement.constant.MessageConstants;
import com.university.schedulemanagement.constant.SecurityConstants;
import com.university.schedulemanagement.dto.request.ChangePasswordRequest;
import com.university.schedulemanagement.dto.request.LoginRequest;
import com.university.schedulemanagement.dto.response.LoginResponse;
import com.university.schedulemanagement.entity.CanBo;
import com.university.schedulemanagement.exception.BadRequestException;
import com.university.schedulemanagement.exception.ResourceNotFoundException;
import com.university.schedulemanagement.exception.UnauthorizedException;
import com.university.schedulemanagement.repository.CanBoRepository;
import com.university.schedulemanagement.config.JwtTokenProvider;
import com.university.schedulemanagement.config.UserPrincipal;
import com.university.schedulemanagement.service.AuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AuthServiceImpl - Implementation của AuthService
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class AuthServiceImpl implements AuthService {

    private final AuthenticationManager authenticationManager;
    private final CanBoRepository canBoRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtTokenProvider tokenProvider;

    // Blacklist tokens để logout
    private final Set<String> blacklistedTokens = ConcurrentHashMap.newKeySet();

    @Override
    public LoginResponse login(LoginRequest request) {
        log.info("Processing login for user: {}", request.getMaCanBo());

        try {
            // Xác thực thông tin đăng nhập
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            request.getMaCanBo(),
                            request.getMatKhau()
                    )
            );

            // Set authentication context
            SecurityContextHolder.getContext().setAuthentication(authentication);

            // Tạo JWT token
            String jwt = tokenProvider.generateToken(authentication);

            // Lấy thông tin user
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            CanBo canBo = canBoRepository.findById(userPrincipal.getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy thông tin người dùng"));

            // Cập nhật thời gian đăng nhập cuối
            canBo.setNgayCapNhat(LocalDateTime.now());
            canBoRepository.save(canBo);

            // Tạo response
            LoginResponse.TeacherInfo teacherInfo = new LoginResponse.TeacherInfo(
                    canBo.getIdCanBo(),
                    canBo.getMaCanBo(),
                    canBo.getTen(),
                    canBo.getEmail(),
                    canBo.getSdt(),
                    canBo.getVaiTro() != null ? canBo.getVaiTro().getTenVaiTro() : null,
                    canBo.getPbmm() != null ? canBo.getPbmm().getTenKhoa() : null,
                    canBo.getNu()
            );

            LoginResponse response = new LoginResponse(
                    jwt,
                    "Bearer",
                    tokenProvider.getExpirationDateFromToken(jwt).getTime(),
                    teacherInfo
            );

            log.info("User {} logged in successfully", request.getMaCanBo());
            return response;

        } catch (Exception e) {
            log.error("Login failed for user: {}, error: {}", request.getMaCanBo(), e.getMessage());
            throw new UnauthorizedException("Tên đăng nhập hoặc mật khẩu không đúng");
        }
    }

    @Override
    public void logout(String token) {
        log.info("Processing logout");

        if (token == null || token.trim().isEmpty()) {
            throw new BadRequestException("Token không được để trống");
        }

        try {
            // Validate token trước khi blacklist
            if (tokenProvider.validateToken(token)) {
                blacklistedTokens.add(token);
                log.info("Token has been blacklisted successfully");
            } else {
                throw new BadRequestException("Token không hợp lệ");
            }
        } catch (Exception e) {
            log.error("Logout failed: {}", e.getMessage());
            throw new BadRequestException("Đăng xuất thất bại: " + e.getMessage());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public CanBo getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null || !authentication.isAuthenticated()) {
            throw new UnauthorizedException("Người dùng chưa đăng nhập");
        }

        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        return canBoRepository.findById(userPrincipal.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy thông tin người dùng"));
    }

    @Override
    public void changePassword(ChangePasswordRequest request) {
        log.info("Processing change password for current user");

        // Validate input
        if (!request.getMatKhauMoi().equals(request.getXacNhanMatKhau())) {
            throw new BadRequestException(MessageConstants.ERROR_PASSWORD_MISMATCH);
        }

        if (request.getMatKhauMoi().length() < SecurityConstants.MIN_PASSWORD_LENGTH) {
            throw new BadRequestException("Mật khẩu mới phải có ít nhất " + SecurityConstants.MIN_PASSWORD_LENGTH + " ký tự");
        }

        // Lấy user hiện tại
        CanBo currentUser = getCurrentUser();

        // Kiểm tra mật khẩu cũ
        if (!passwordEncoder.matches(request.getMatKhauCu(), currentUser.getMatKhau())) {
            throw new BadRequestException(MessageConstants.ERROR_WRONG_PASSWORD);
        }

        // Kiểm tra mật khẩu mới không trùng mật khẩu cũ
        if (passwordEncoder.matches(request.getMatKhauMoi(), currentUser.getMatKhau())) {
            throw new BadRequestException("Mật khẩu mới phải khác mật khẩu cũ");
        }

        // Cập nhật mật khẩu mới
        currentUser.setMatKhau(passwordEncoder.encode(request.getMatKhauMoi()));
        currentUser.setNgayCapNhat(LocalDateTime.now());
        canBoRepository.save(currentUser);

        log.info("Password changed successfully for user: {}", currentUser.getMaCanBo());
    }

    @Override
    public void resetPassword(String maCanBo) {
        log.info("Processing reset password for user: {}", maCanBo);

        if (maCanBo == null || maCanBo.trim().isEmpty()) {
            throw new BadRequestException("Mã cán bộ không được để trống");
        }

        CanBo canBo = canBoRepository.findByMaCanBo(maCanBo)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy người dùng với mã: " + maCanBo));

        // Reset về mật khẩu mặc định
        canBo.setMatKhau(passwordEncoder.encode(SecurityConstants.DEFAULT_PASSWORD));
        canBo.setNgayCapNhat(LocalDateTime.now());
        canBoRepository.save(canBo);

        log.info("Password reset successfully for user: {}", maCanBo);

        // TODO: Gửi email thông báo reset mật khẩu
        // emailService.sendPasswordResetNotification(canBo);
    }

    @Override
    public boolean validateToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }

        // Kiểm tra token có trong blacklist không
        if (blacklistedTokens.contains(token)) {
            log.warn("Token is blacklisted");
            return false;
        }

        return tokenProvider.validateToken(token);
    }

    /**
     * Lấy thông tin user từ token (dùng cho internal service)
     */
    @Transactional(readOnly = true)
    public CanBo getUserFromToken(String token) {
        if (!validateToken(token)) {
            throw new UnauthorizedException("Token không hợp lệ");
        }

        String maCanBo = tokenProvider.getMaCanBoFromToken(token);
        return canBoRepository.findByMaCanBoAndTrangThaiTrue(maCanBo)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy người dùng"));
    }

    /**
     * Kiểm tra quyền của user hiện tại
     */
    public boolean hasRole(String role) {
        try {
            CanBo currentUser = getCurrentUser();
            return currentUser.getVaiTro() != null &&
                    role.equalsIgnoreCase(currentUser.getVaiTro().getTenVaiTro());
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Kiểm tra user hiện tại có phải là admin không
     */
    @Override
    public boolean isAdmin() {
        return hasRole(SecurityConstants.ROLE_ADMIN);
    }

    /**
     * Kiểm tra user hiện tại có phải là trưởng khoa không
     */
    @Override
    public boolean isTruongKhoa() {
        return hasRole(SecurityConstants.ROLE_TRUONG_KHOA);
    }

    /**
     * Kiểm tra user hiện tại có phải là giảng viên không
     */
    @Override
    public boolean isGiangVien() {
        return hasRole(SecurityConstants.ROLE_GIANG_VIEN);
    }

    /**
     * Kiểm tra user có quyền truy cập dữ liệu của khoa không
     */
    @Override
    public boolean canAccessDepartmentData(Long departmentId) {
        try {
            CanBo currentUser = getCurrentUser();

            // Admin có thể truy cập tất cả
            if (isAdmin()) {
                return true;
            }

            // Trưởng khoa và giảng viên chỉ truy cập được khoa của mình
            return currentUser.getIdPbmm().equals(departmentId);

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Kiểm tra user có quyền xem dữ liệu của giảng viên khác không
     */
    @Override
    public boolean canAccessTeacherData(Long teacherId) {
        try {
            CanBo currentUser = getCurrentUser();

            // Giảng viên chỉ xem được dữ liệu của chính mình
            if (isGiangVien()) {
                return currentUser.getIdCanBo().equals(teacherId);
            }

            // Admin có thể xem tất cả
            if (isAdmin()) {
                return true;
            }

            // Trưởng khoa xem được giảng viên trong khoa
            if (isTruongKhoa()) {
                CanBo teacher = canBoRepository.findById(teacherId)
                        .orElse(null);
                return teacher != null &&
                        currentUser.getIdPbmm().equals(teacher.getIdPbmm());
            }

            return false;

        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Clear blacklisted tokens (scheduled task)
     */
    @Scheduled(fixedRate = 3600000) // Chạy mỗi giờ
    public void clearExpiredTokens() {
        blacklistedTokens.removeIf(token -> {
            try {
                return !tokenProvider.validateToken(token);
            } catch (Exception e) {
                return true; // Remove invalid tokens
            }
        });

        log.debug("Cleared expired blacklisted tokens. Current size: {}", blacklistedTokens.size());
    }
}
