package com.university.schedulemanagement.serviceImpl;

import com.university.schedulemanagement.dto.request.AcademicYearRequest;
import com.university.schedulemanagement.dto.response.AcademicYearResponse;
import com.university.schedulemanagement.dto.response.PageResponse;
import com.university.schedulemanagement.dto.response.SemesterResponse;
import com.university.schedulemanagement.entity.NienKhoa;
import com.university.schedulemanagement.entity.HocKy;
import com.university.schedulemanagement.exception.BadRequestException;
import com.university.schedulemanagement.exception.ResourceNotFoundException;
import com.university.schedulemanagement.repository.NienKhoaRepository;
import com.university.schedulemanagement.repository.HocKyRepository;
import com.university.schedulemanagement.repository.LichGiangRepository;
import com.university.schedulemanagement.service.AcademicYearService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AcademicYearServiceImpl - Implementation của AcademicYearService
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class AcademicYearServiceImpl implements AcademicYearService {

    private final NienKhoaRepository nienKhoaRepository;
    private final HocKyRepository hocKyRepository;
    private final LichGiangRepository lichGiangRepository;

    @Override
    @Transactional(readOnly = true)
    public PageResponse<AcademicYearResponse> getAllAcademicYears(Pageable pageable) {
        log.info("Getting all academic years with pagination");
        
        Page<NienKhoa> nienKhoaPage = nienKhoaRepository.findAll(pageable);
        
        List<AcademicYearResponse> responses = nienKhoaPage.getContent().stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
        
        return PageResponse.<AcademicYearResponse>builder()
                .content(responses)
                .page(nienKhoaPage.getNumber())
                .size(nienKhoaPage.getSize())
                .totalElements(nienKhoaPage.getTotalElements())
                .totalPages(nienKhoaPage.getTotalPages())
                .first(nienKhoaPage.isFirst())
                .last(nienKhoaPage.isLast())
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public List<AcademicYearResponse> getActiveAcademicYears() {
        log.info("Getting active academic years");
        
        List<NienKhoa> activeNienKhoas = nienKhoaRepository.findByTrangThaiTrueOrderByNamDesc();
        
        return activeNienKhoas.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public AcademicYearResponse getAcademicYearById(Long id) {
        log.info("Getting academic year with ID: {}", id);
        
        NienKhoa nienKhoa = nienKhoaRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy năm học với ID: " + id));
        
        return mapToResponseWithDetails(nienKhoa);
    }

    @Override
    public AcademicYearResponse createAcademicYear(AcademicYearRequest request) {
        log.info("Creating new academic year: {}", request.getTenNienKhoa());
        
        // Validate dữ liệu
        validateAcademicYearRequest(request, null);
        
        // Tạo entity mới
        NienKhoa nienKhoa = new NienKhoa();
        mapRequestToEntity(request, nienKhoa);
        
        // Lưu vào database
        nienKhoa = nienKhoaRepository.save(nienKhoa);
        
        log.info("Academic year created successfully with ID: {}", nienKhoa.getIdNienKhoa());
        return mapToResponse(nienKhoa);
    }

    @Override
    public AcademicYearResponse updateAcademicYear(Long id, AcademicYearRequest request) {
        log.info("Updating academic year {}: {}", id, request.getTenNienKhoa());
        
        // Kiểm tra tồn tại
        NienKhoa existingNienKhoa = nienKhoaRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy năm học với ID: " + id));
        
        // Validate dữ liệu
        validateAcademicYearRequest(request, id);
        
        // Cập nhật thông tin
        mapRequestToEntity(request, existingNienKhoa);
        
        // Lưu vào database
        existingNienKhoa = nienKhoaRepository.save(existingNienKhoa);
        
        log.info("Academic year updated successfully: {}", id);
        return mapToResponse(existingNienKhoa);
    }

    @Override
    public void deleteAcademicYear(Long id) {
        log.info("Deleting academic year with ID: {}", id);
        
        // Kiểm tra tồn tại
        NienKhoa nienKhoa = nienKhoaRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy năm học với ID: " + id));
        
        // Kiểm tra có thể xóa không
        if (!canDeleteAcademicYear(id)) {
            throw new BadRequestException("Không thể xóa năm học này vì đã có dữ liệu liên quan");
        }
        
        // Xóa năm học
        nienKhoaRepository.delete(nienKhoa);
        
        log.info("Academic year deleted successfully: {}", id);
    }

    @Override
    public void activateAcademicYear(Long id) {
        log.info("Activating academic year with ID: {}", id);
        
        // Kiểm tra tồn tại
        NienKhoa nienKhoa = nienKhoaRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy năm học với ID: " + id));
        
        // Kích hoạt năm học (logic có thể mở rộng thêm)
        nienKhoa.setTrangThai(true);
        nienKhoaRepository.save(nienKhoa);
        
        log.info("Academic year activated successfully: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public AcademicYearResponse getCurrentAcademicYear() {
        log.info("Getting current academic year");
        
        // Lấy năm học hiện tại (có thể dựa vào năm hiện tại hoặc cấu hình)
        List<NienKhoa> activeYears = nienKhoaRepository.findByTrangThaiTrueOrderByNamDesc();
        
        if (activeYears.isEmpty()) {
            throw new ResourceNotFoundException("Không tìm thấy năm học hiện tại");
        }
        
        return mapToResponse(activeYears.get(0));
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canDeleteAcademicYear(Long id) {
        log.info("Checking if academic year {} can be deleted", id);
        
        // Kiểm tra có học kỳ nào không
        Long semesterCount = hocKyRepository.countByIdNienKhoa(id);
        
        return semesterCount == 0;
    }

    @Override
    @Transactional(readOnly = true)
    public Object getAcademicYearStatistics(Long id) {
        log.info("Getting statistics for academic year: {}", id);
        
        // Kiểm tra tồn tại
        NienKhoa nienKhoa = nienKhoaRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy năm học với ID: " + id));
        
        Map<String, Object> statistics = new HashMap<>();
        
        // Thống kê học kỳ
        Long semesterCount = hocKyRepository.countByIdNienKhoa(id);
        statistics.put("totalSemesters", semesterCount);
        
        // Thống kê lịch giảng (nếu có)
        // Long scheduleCount = lichGiangRepository.countByNienKhoa(id);
        // statistics.put("totalSchedules", scheduleCount);
        
        statistics.put("academicYear", mapToResponse(nienKhoa));
        
        return statistics;
    }

    // ==================== PRIVATE METHODS ====================
    
    private void validateAcademicYearRequest(AcademicYearRequest request, Long excludeId) {
        // Kiểm tra trùng tên niên khóa
        if (nienKhoaRepository.existsByTenNienKhoa(request.getTenNienKhoa())) {
            if (excludeId == null) {
                throw new BadRequestException("Tên niên khóa đã tồn tại: " + request.getTenNienKhoa());
            } else {
                // Kiểm tra trùng với record khác (không phải record đang update)
                NienKhoa existing = nienKhoaRepository.findByTenNienKhoa(request.getTenNienKhoa()).orElse(null);
                if (existing != null && !existing.getIdNienKhoa().equals(excludeId)) {
                    throw new BadRequestException("Tên niên khóa đã tồn tại: " + request.getTenNienKhoa());
                }
            }
        }
    }

    private void mapRequestToEntity(AcademicYearRequest request, NienKhoa nienKhoa) {
        nienKhoa.setTenNienKhoa(request.getTenNienKhoa());
        nienKhoa.setNam(request.getNam());
        nienKhoa.setIdThongTu(request.getIdThongTu());
        nienKhoa.setTrangThai(request.getTrangThai());
    }

    private AcademicYearResponse mapToResponse(NienKhoa nienKhoa) {
        AcademicYearResponse response = new AcademicYearResponse();
        response.setIdNienKhoa(nienKhoa.getIdNienKhoa());
        response.setTenNienKhoa(nienKhoa.getTenNienKhoa());
        response.setNam(nienKhoa.getNam());
        response.setIdThongTu(nienKhoa.getIdThongTu());
        response.setTrangThai(nienKhoa.getTrangThai());
        response.setNgayTao(nienKhoa.getNgayTao());
        response.setNgayCapNhat(nienKhoa.getNgayCapNhat());
        
        // Thống kê cơ bản
        Long semesterCount = hocKyRepository.countByIdNienKhoa(nienKhoa.getIdNienKhoa());
        response.setSoHocKy(semesterCount.intValue());
        
        return response;
    }

    private AcademicYearResponse mapToResponseWithDetails(NienKhoa nienKhoa) {
        AcademicYearResponse response = mapToResponse(nienKhoa);
        
        // Thêm danh sách học kỳ
        List<HocKy> hocKyList = hocKyRepository.findByIdNienKhoaAndTrangThaiTrueOrderByNgayBatDau(nienKhoa.getIdNienKhoa());
        List<SemesterResponse> semesterResponses = hocKyList.stream()
                .map(this::mapHocKyToSemesterResponse)
                .collect(Collectors.toList());
        response.setHocKyList(semesterResponses);
        
        return response;
    }

    private SemesterResponse mapHocKyToSemesterResponse(HocKy hocKy) {
        SemesterResponse response = new SemesterResponse();
        response.setIdHocKy(hocKy.getIdHocKy());
        response.setIdNienKhoa(hocKy.getIdNienKhoa());
        response.setTenHocKy(hocKy.getTenHocKy());
        response.setSoTuan(hocKy.getSoTuan());
        response.setNgayBatDau(hocKy.getNgayBatDau());
        response.setNgayKetThuc(hocKy.getNgayKetThuc());
        response.setHienTai(hocKy.getHienTai());
        response.setTrangThai(hocKy.getTrangThai());
        response.setNgayTao(hocKy.getNgayTao());
        response.setNgayCapNhat(hocKy.getNgayCapNhat());
        return response;
    }
}
