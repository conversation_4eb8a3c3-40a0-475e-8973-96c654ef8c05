package com.university.schedulemanagement.serviceImpl;

import com.university.schedulemanagement.dto.request.*;
import com.university.schedulemanagement.dto.response.PageResponse;
import com.university.schedulemanagement.entity.*;
import com.university.schedulemanagement.exception.BadRequestException;
import com.university.schedulemanagement.exception.ResourceNotFoundException;
import com.university.schedulemanagement.repository.*;
import com.university.schedulemanagement.service.MasterDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * MasterDataServiceImpl - Implementation của MasterDataService
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class MasterDataServiceImpl implements MasterDataService {

    private final PBMMRepository pbmmRepository;
    private final MonHocRepository monHocRepository;
    private final LopHocRepository lopHocRepository;
    private final PhongHocRepository phongHocRepository;
    private final CoSoRepository coSoRepository;
    private final CanBoRepository canBoRepository;
    private final NganhHocRepository nganhHocRepository;
    private final PasswordEncoder passwordEncoder;

    // ==================== QUẢN LÝ KHOA/PHÒNG BAN ====================
    
    @Override
    @Transactional(readOnly = true)
    public PageResponse<Object> getAllDepartments(Pageable pageable) {
        log.info("Getting all departments with pagination");
        
        Page<PBMM> pbmmPage = pbmmRepository.findAll(pageable);
        
        List<Object> responses = pbmmPage.getContent().stream()
                .map(this::mapPBMMToResponse)
                .collect(Collectors.toList());
        
        return PageResponse.builder()
                .content(responses)
                .page(pbmmPage.getNumber())
                .size(pbmmPage.getSize())
                .totalElements(pbmmPage.getTotalElements())
                .totalPages(pbmmPage.getTotalPages())
                .first(pbmmPage.isFirst())
                .last(pbmmPage.isLast())
                .build();
    }

    @Override
    public Object createDepartment(DepartmentRequest request) {
        log.info("Creating new department: {}", request.getTenKhoa());
        
        // Validate dữ liệu
        validateDepartmentRequest(request, null);
        
        // Tạo entity mới
        PBMM pbmm = new PBMM();
        mapDepartmentRequestToEntity(request, pbmm);
        
        // Lưu vào database
        pbmm = pbmmRepository.save(pbmm);
        
        log.info("Department created successfully with ID: {}", pbmm.getIdPbmm());
        return mapPBMMToResponse(pbmm);
    }

    @Override
    public Object updateDepartment(Long id, DepartmentRequest request) {
        log.info("Updating department {}: {}", id, request.getTenKhoa());
        
        // Kiểm tra tồn tại
        PBMM existingPBMM = pbmmRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy khoa với ID: " + id));
        
        // Validate dữ liệu
        validateDepartmentRequest(request, id);
        
        // Cập nhật thông tin
        mapDepartmentRequestToEntity(request, existingPBMM);
        
        // Lưu vào database
        existingPBMM = pbmmRepository.save(existingPBMM);
        
        log.info("Department updated successfully: {}", id);
        return mapPBMMToResponse(existingPBMM);
    }

    @Override
    public void deleteDepartment(Long id) {
        log.info("Deleting department with ID: {}", id);
        
        // Kiểm tra tồn tại
        PBMM pbmm = pbmmRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy khoa với ID: " + id));
        
        // Kiểm tra có thể xóa không (có cán bộ, môn học liên quan)
        if (canBoRepository.countByIdPbmm(id) > 0) {
            throw new BadRequestException("Không thể xóa khoa này vì đã có cán bộ");
        }
        
        if (monHocRepository.countByIdPbmm(id) > 0) {
            throw new BadRequestException("Không thể xóa khoa này vì đã có môn học");
        }
        
        // Xóa khoa
        pbmmRepository.delete(pbmm);
        
        log.info("Department deleted successfully: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public Object getDepartmentById(Long id) {
        log.info("Getting department with ID: {}", id);
        
        PBMM pbmm = pbmmRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy khoa với ID: " + id));
        
        return mapPBMMToResponseWithDetails(pbmm);
    }

    // ==================== QUẢN LÝ MÔN HỌC ====================
    
    @Override
    @Transactional(readOnly = true)
    public PageResponse<Object> getAllSubjects(Long departmentId, Pageable pageable) {
        log.info("Getting all subjects for department: {}", departmentId);
        
        Page<MonHoc> monHocPage;
        
        if (departmentId != null) {
            monHocPage = monHocRepository.findByIdPbmmWithPagination(departmentId, pageable);
        } else {
            monHocPage = monHocRepository.findAll(pageable);
        }
        
        List<Object> responses = monHocPage.getContent().stream()
                .map(this::mapMonHocToResponse)
                .collect(Collectors.toList());
        
        return PageResponse.builder()
                .content(responses)
                .page(monHocPage.getNumber())
                .size(monHocPage.getSize())
                .totalElements(monHocPage.getTotalElements())
                .totalPages(monHocPage.getTotalPages())
                .first(monHocPage.isFirst())
                .last(monHocPage.isLast())
                .build();
    }

    @Override
    public Object createSubject(SubjectRequest request) {
        log.info("Creating new subject: {}", request.getTenMonHoc());
        
        // Validate dữ liệu
        validateSubjectRequest(request, null);
        
        // Tạo entity mới
        MonHoc monHoc = new MonHoc();
        mapSubjectRequestToEntity(request, monHoc);
        
        // Lưu vào database
        monHoc = monHocRepository.save(monHoc);
        
        log.info("Subject created successfully with ID: {}", monHoc.getIdMonHoc());
        return mapMonHocToResponse(monHoc);
    }

    @Override
    public Object updateSubject(Long id, SubjectRequest request) {
        log.info("Updating subject {}: {}", id, request.getTenMonHoc());
        
        // Kiểm tra tồn tại
        MonHoc existingMonHoc = monHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy môn học với ID: " + id));
        
        // Validate dữ liệu
        validateSubjectRequest(request, id);
        
        // Cập nhật thông tin
        mapSubjectRequestToEntity(request, existingMonHoc);
        
        // Lưu vào database
        existingMonHoc = monHocRepository.save(existingMonHoc);
        
        log.info("Subject updated successfully: {}", id);
        return mapMonHocToResponse(existingMonHoc);
    }

    @Override
    public void deleteSubject(Long id) {
        log.info("Deleting subject with ID: {}", id);
        
        // Kiểm tra tồn tại
        MonHoc monHoc = monHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy môn học với ID: " + id));
        
        // Kiểm tra có thể xóa không (có lịch giảng liên quan)
        // Long scheduleCount = lichGiangRepository.countByIdMonHoc(id);
        // if (scheduleCount > 0) {
        //     throw new BadRequestException("Không thể xóa môn học này vì đã có lịch giảng");
        // }
        
        // Xóa môn học
        monHocRepository.delete(monHoc);
        
        log.info("Subject deleted successfully: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public Object getSubjectById(Long id) {
        log.info("Getting subject with ID: {}", id);
        
        MonHoc monHoc = monHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy môn học với ID: " + id));
        
        return mapMonHocToResponse(monHoc);
    }

    // ==================== QUẢN LÝ LỚP HỌC ====================
    
    @Override
    @Transactional(readOnly = true)
    public PageResponse<Object> getAllClasses(Long departmentId, Pageable pageable) {
        log.info("Getting all classes for department: {}", departmentId);
        
        Page<LopHoc> lopHocPage;
        
        if (departmentId != null) {
            lopHocPage = lopHocRepository.findByDepartmentWithPagination(departmentId, pageable);
        } else {
            lopHocPage = lopHocRepository.findAll(pageable);
        }
        
        List<Object> responses = lopHocPage.getContent().stream()
                .map(this::mapLopHocToResponse)
                .collect(Collectors.toList());
        
        return PageResponse.builder()
                .content(responses)
                .page(lopHocPage.getNumber())
                .size(lopHocPage.getSize())
                .totalElements(lopHocPage.getTotalElements())
                .totalPages(lopHocPage.getTotalPages())
                .first(lopHocPage.isFirst())
                .last(lopHocPage.isLast())
                .build();
    }

    @Override
    public Object createClass(ClassRequest request) {
        log.info("Creating new class: {}", request.getTenLop());
        
        // Validate dữ liệu
        validateClassRequest(request, null);
        
        // Tạo entity mới
        LopHoc lopHoc = new LopHoc();
        mapClassRequestToEntity(request, lopHoc);
        
        // Lưu vào database
        lopHoc = lopHocRepository.save(lopHoc);
        
        log.info("Class created successfully with ID: {}", lopHoc.getIdLop());
        return mapLopHocToResponse(lopHoc);
    }

    @Override
    public Object updateClass(Long id, ClassRequest request) {
        log.info("Updating class {}: {}", id, request.getTenLop());
        
        // Kiểm tra tồn tại
        LopHoc existingLopHoc = lopHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy lớp học với ID: " + id));
        
        // Validate dữ liệu
        validateClassRequest(request, id);
        
        // Cập nhật thông tin
        mapClassRequestToEntity(request, existingLopHoc);
        
        // Lưu vào database
        existingLopHoc = lopHocRepository.save(existingLopHoc);
        
        log.info("Class updated successfully: {}", id);
        return mapLopHocToResponse(existingLopHoc);
    }

    @Override
    public void deleteClass(Long id) {
        log.info("Deleting class with ID: {}", id);
        
        // Kiểm tra tồn tại
        LopHoc lopHoc = lopHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy lớp học với ID: " + id));
        
        // Kiểm tra có thể xóa không
        // Long scheduleCount = lichGiangRepository.countByIdLop(id);
        // if (scheduleCount > 0) {
        //     throw new BadRequestException("Không thể xóa lớp học này vì đã có lịch giảng");
        // }
        
        // Xóa lớp học
        lopHocRepository.delete(lopHoc);
        
        log.info("Class deleted successfully: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public Object getClassById(Long id) {
        log.info("Getting class with ID: {}", id);
        
        LopHoc lopHoc = lopHocRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy lớp học với ID: " + id));
        
        return mapLopHocToResponse(lopHoc);
    }

    // ==================== PRIVATE VALIDATION METHODS ====================
    
    private void validateDepartmentRequest(DepartmentRequest request, Long excludeId) {
        // Kiểm tra trùng mã khoa
        if (pbmmRepository.existsByMaKhoa(request.getMaKhoa())) {
            if (excludeId == null) {
                throw new BadRequestException("Mã khoa đã tồn tại: " + request.getMaKhoa());
            }
            // Thêm logic kiểm tra cho update
        }
        
        // Kiểm tra trùng tên khoa
        if (pbmmRepository.existsByTenKhoa(request.getTenKhoa())) {
            if (excludeId == null) {
                throw new BadRequestException("Tên khoa đã tồn tại: " + request.getTenKhoa());
            }
            // Thêm logic kiểm tra cho update
        }
    }

    private void validateSubjectRequest(SubjectRequest request, Long excludeId) {
        // Kiểm tra khoa tồn tại
        pbmmRepository.findById(request.getIdPbmm())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy khoa với ID: " + request.getIdPbmm()));
        
        // Kiểm tra trùng mã môn học
        if (monHocRepository.existsByMaMonHoc(request.getMaMonHoc())) {
            if (excludeId == null) {
                throw new BadRequestException("Mã môn học đã tồn tại: " + request.getMaMonHoc());
            }
            // Thêm logic kiểm tra cho update
        }
    }

    private void validateClassRequest(ClassRequest request, Long excludeId) {
        // Kiểm tra ngành học tồn tại
        nganhHocRepository.findById(request.getIdNganhHoc())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy ngành học với ID: " + request.getIdNganhHoc()));
        
        // Kiểm tra trùng mã lớp
        if (lopHocRepository.existsByMaLop(request.getMaLop())) {
            if (excludeId == null) {
                throw new BadRequestException("Mã lớp đã tồn tại: " + request.getMaLop());
            }
            // Thêm logic kiểm tra cho update
        }
    }

    // ==================== PRIVATE MAPPING METHODS ====================
    
    private void mapDepartmentRequestToEntity(DepartmentRequest request, PBMM pbmm) {
        pbmm.setMaKhoa(request.getMaKhoa());
        pbmm.setTenKhoa(request.getTenKhoa());
        pbmm.setTrangThai(request.getTrangThai());
    }

    private void mapSubjectRequestToEntity(SubjectRequest request, MonHoc monHoc) {
        monHoc.setMaMonHoc(request.getMaMonHoc());
        monHoc.setTenMonHoc(request.getTenMonHoc());
        monHoc.setIdPbmm(request.getIdPbmm());
        monHoc.setIdLoaiMonHoc(request.getIdLoaiMonHoc());
        monHoc.setIdHeDaoTao(request.getIdHeDaoTao());
        monHoc.setIdNhomLk(request.getIdNhomLk());
        monHoc.setSoTietLt(request.getSoTietLt());
        monHoc.setSoTietTh(request.getSoTietTh());
        monHoc.setSoTietTu(request.getSoTietTu());
        monHoc.setMonDieuKien(request.getMonDieuKien());
        monHoc.setMonTn(request.getMonTn());
        monHoc.setTrangThai(true);
    }

    private void mapClassRequestToEntity(ClassRequest request, LopHoc lopHoc) {
        lopHoc.setMaLop(request.getMaLop());
        lopHoc.setTenLop(request.getTenLop());
        lopHoc.setIdNganh(request.getIdNganhHoc());
        lopHoc.setIdHeDaoTao(request.getIdHeDaoTao());
        lopHoc.setKhoaHoc(request.getKhoaHoc());
        lopHoc.setSiSo(request.getSiSo());
        lopHoc.setTrangThai(request.getTrangThai());
    }

    private Object mapPBMMToResponse(PBMM pbmm) {
        Map<String, Object> response = new HashMap<>();
        response.put("idPbmm", pbmm.getIdPbmm());
        response.put("maKhoa", pbmm.getMaKhoa());
        response.put("tenKhoa", pbmm.getTenKhoa());
        response.put("trangThai", pbmm.getTrangThai());
        response.put("ngayTao", pbmm.getNgayTao());
        response.put("ngayCapNhat", pbmm.getNgayCapNhat());
        return response;
    }

    private Object mapPBMMToResponseWithDetails(PBMM pbmm) {
        Map<String, Object> response = (Map<String, Object>) mapPBMMToResponse(pbmm);
        
        // Thêm thống kê
        Long teacherCount = canBoRepository.countByIdPbmm(pbmm.getIdPbmm());
        Long subjectCount = monHocRepository.countByIdPbmm(pbmm.getIdPbmm());
        
        response.put("soGiangVien", teacherCount);
        response.put("soMonHoc", subjectCount);
        
        return response;
    }

    private Object mapMonHocToResponse(MonHoc monHoc) {
        Map<String, Object> response = new HashMap<>();
        response.put("idMonHoc", monHoc.getIdMonHoc());
        response.put("maMonHoc", monHoc.getMaMonHoc());
        response.put("tenMonHoc", monHoc.getTenMonHoc());
        response.put("idPbmm", monHoc.getIdPbmm());
        response.put("soTietLt", monHoc.getSoTietLt());
        response.put("soTietTh", monHoc.getSoTietTh());
        response.put("soTietTu", monHoc.getSoTietTu());
        response.put("trangThai", monHoc.getTrangThai());
        response.put("ngayTao", monHoc.getNgayTao());
        response.put("ngayCapNhat", monHoc.getNgayCapNhat());
        return response;
    }

    private Object mapLopHocToResponse(LopHoc lopHoc) {
        Map<String, Object> response = new HashMap<>();
        response.put("idLop", lopHoc.getIdLop());
        response.put("maLop", lopHoc.getMaLop());
        response.put("tenLop", lopHoc.getTenLop());
        response.put("idNganh", lopHoc.getIdNganh());
        response.put("idHeDaoTao", lopHoc.getIdHeDaoTao());
        response.put("khoaHoc", lopHoc.getKhoaHoc());
        response.put("siSo", lopHoc.getSiSo());
        response.put("trangThai", lopHoc.getTrangThai());
        response.put("ngayTao", lopHoc.getNgayTao());
        response.put("ngayCapNhat", lopHoc.getNgayCapNhat());
        return response;
    }

    // ==================== CHƯA TRIỂN KHAI ĐẦY ĐỦ ====================
    // Các methods còn lại sẽ được triển khai tương tự...
    
    @Override
    public PageResponse<Object> getAllRooms(Long campusId, Pageable pageable) {
        // TODO: Implement
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public Object createRoom(RoomRequest request) {
        // TODO: Implement
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public Object updateRoom(Long id, RoomRequest request) {
        // TODO: Implement
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public void deleteRoom(Long id) {
        // TODO: Implement
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public Object getRoomById(Long id) {
        // TODO: Implement
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public List<Object> getAllCampuses() {
        // TODO: Implement
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public Object createCampus(CampusRequest request) {
        // TODO: Implement
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public Object updateCampus(Long id, CampusRequest request) {
        // TODO: Implement
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public void deleteCampus(Long id) {
        // TODO: Implement
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public PageResponse<Object> getAllTeachers(Long departmentId, Pageable pageable) {
        // TODO: Implement
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public Object createTeacher(TeacherRequest request) {
        // TODO: Implement
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public Object updateTeacher(Long id, TeacherRequest request) {
        // TODO: Implement
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public void deleteTeacher(Long id) {
        // TODO: Implement
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public Object importData(String dataType, MultipartFile file) {
        // TODO: Implement với Apache POI
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public byte[] exportTemplate(String dataType) {
        // TODO: Implement với Apache POI
        throw new UnsupportedOperationException("Chưa triển khai");
    }

    @Override
    public Object syncDataFromExternalSystem(String systemType, Object config) {
        // TODO: Implement
        throw new UnsupportedOperationException("Chưa triển khai");
    }
}
