package com.university.schedulemanagement.service;

import com.university.schedulemanagement.dto.response.DashboardResponse;

/**
 * AdminService - Service quản trị hệ thống
 */
public interface AdminService {

    /**
     * L<PERSON>y dữ liệu dashboard cho admin
     */
    DashboardResponse getDashboardData();

    /**
     * L<PERSON><PERSON> thông tin chi tiết hệ thống
     */
    Object getSystemInfo();

    /**
     * Khởi tạo lại dữ liệu mẫu cho hệ thống
     */
    void initializeSystemData();

    /**
     * Tạo bản sao lưu dữ liệu
     */
    String createBackup();

    /**
     * L<PERSON>y thống kê tổng quan hệ thống
     */
    Object getSystemStatistics();

    /**
     * Kiểm tra tình trạng hệ thống
     */
    Object checkSystemHealth();

    /**
     * Dọn dẹp dữ liệu cũ
     */
    void cleanupOldData();

    /**
     * Tối ưu hóa database
     */
    void optimizeDatabase();
}
