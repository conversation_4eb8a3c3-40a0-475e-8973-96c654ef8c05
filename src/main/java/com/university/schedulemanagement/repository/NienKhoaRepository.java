package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;


/**
 * NienKhoaRepository - Repository cho Niên khoá
 */
@Repository
public interface NienKhoaRepository extends JpaRepository<NienKhoa, Long> {

    List<NienKhoa> findByNam(Integer nam);

    @Query("SELECT nk FROM NienKhoa nk ORDER BY nk.nam DESC")
    List<NienKhoa> findAllOrderByYearDesc();

    @Query("SELECT nk FROM NienKhoa nk WHERE nk.trangThai = true ORDER BY nk.nam DESC")
    List<NienKhoa> findByTrangThaiTrueOrderByNamDesc();

    @Query("SELECT COUNT(nk) FROM NienKhoa nk WHERE nk.trangThai = true")
    Long countByTrangThaiTrue();

    @Query("SELECT nk FROM NienKhoa nk WHERE " +
            "(LOWER(nk.tenNienKhoa) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "CAST(nk.nam AS string) LIKE CONCAT('%', :keyword, '%'))")
    Page<NienKhoa> findByKeyword(@Param("keyword") String keyword, Pageable pageable);

    Optional<NienKhoa> findByTenNienKhoa(String tenNienKhoa);

    boolean existsByNam(Integer nam);

    boolean existsByTenNienKhoa(String tenNienKhoa);
}
