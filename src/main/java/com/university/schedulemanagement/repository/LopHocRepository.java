package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * LopHocRepository - Repository cho Lớp học
 */
@Repository
public interface LopHocRepository extends JpaRepository<LopHoc, Long> {

    Optional<LopHoc> findByMaLop(String maLop);

    List<LopHoc> findByIdNganh(Long idNganh);

    List<LopHoc> findByIdHeDaoTao(Long idHeDaoTao);

    @Query("SELECT lh FROM LopHoc lh WHERE lh.idNganh = :idNganh AND lh.idHeDaoTao = :idHeDaoTao")
    List<LopHoc> findByNganhAndHeDaoTao(@Param("idNganh") Long idNganh, @Param("idHeDaoTao") Long idHeDaoTao);

    @Query("SELECT lh FROM LopHoc lh WHERE " +
            "(LOWER(lh.tenLop) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "LOWER(lh.maLop) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<LopHoc> findByKeyword(@Param("keyword") String keyword, Pageable pageable);

    @Query("SELECT lh FROM LopHoc lh JOIN lh.nganhHoc nh WHERE nh.idPbmm = :idPbmm")
    Page<LopHoc> findByDepartmentWithPagination(@Param("idPbmm") Long idPbmm, Pageable pageable);

    @Query("SELECT COUNT(lh) FROM LopHoc lh WHERE lh.trangThai = true")
    Long countByTrangThaiTrue();

    boolean existsByMaLop(String maLop);
}
