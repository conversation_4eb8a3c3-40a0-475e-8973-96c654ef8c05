package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * MonHocRepository - Repository cho Môn học
 */
@Repository
public interface MonHocRepository extends JpaRepository<MonHoc, Long> {

    Optional<MonHoc> findByMaMonHoc(String maMonHoc);

    List<MonHoc> findByIdPbmm(Long idPbmm);

    List<MonHoc> findByIdHeDaoTao(Long idHeDaoTao);

    List<MonHoc> findByIdLoaiMonHoc(Long idLoaiMonHoc);

    @Query("SELECT mh FROM MonHoc mh WHERE " +
            "(LOWER(mh.tenMonHoc) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "LOWER(mh.maMonHoc) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<MonHoc> findByKeyword(@Param("keyword") String keyword, Pageable pageable);

    @Query("SELECT mh FROM MonHoc mh WHERE mh.idPbmm = :idPbmm")
    Page<MonHoc> findByIdPbmmWithPagination(@Param("idPbmm") Long idPbmm, Pageable pageable);

    @Query("SELECT COUNT(mh) FROM MonHoc mh WHERE mh.idPbmm = :idPbmm")
    Long countByIdPbmm(@Param("idPbmm") Long idPbmm);

    @Query("SELECT COUNT(mh) FROM MonHoc mh WHERE mh.trangThai = true")
    Long countByTrangThaiTrue();

    boolean existsByMaMonHoc(String maMonHoc);

    @Query("SELECT mh FROM MonHoc mh WHERE mh.idPbmm = :idPbmm AND mh.idHeDaoTao = :idHeDaoTao")
    List<MonHoc> findByKhoaAndHeDaoTao(@Param("idPbmm") Long idPbmm, @Param("idHeDaoTao") Long idHeDaoTao);
}
