package com.university.schedulemanagement.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * AcademicYearResponse - DTO response cho năm học/niên khóa
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AcademicYearResponse {

    private Long idNienKhoa;
    private String tenNienKhoa;
    private Integer nam;
    private Long idThongTu;
    private String moTa;
    private Boolean trangThai;
    private LocalDateTime ngayTao;
    private LocalDateTime ngayCapNhat;
    
    // Thông tin thống kê
    private Integer soHocKy;
    private Integer soLichGiang;
    private Boolean isActive;
    
    // Danh sách học kỳ (nếu cần)
    private List<SemesterResponse> hocKyList;
}
