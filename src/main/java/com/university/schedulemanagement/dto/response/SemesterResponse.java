package com.university.schedulemanagement.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * SemesterResponse - DTO response cho họ<PERSON> kỳ
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SemesterResponse {

    private Long idHocKy;
    private Long idNienKhoa;
    private String tenHocKy;
    private Integer soTuan;
    private LocalDate ngayBatDau;
    private LocalDate ngayKetThuc;
    private Boolean hienTai;
    private String moTa;
    private Boolean trangThai;
    private LocalDateTime ngayTao;
    private LocalDateTime ngayCapNhat;
    
    // Thông tin niên khóa
    private String tenNienKhoa;
    private Integer namNienKhoa;
    
    // Thông tin thống kê
    private Integer soLichGiang;
    private Integer soGiangVien;
    private Integer soLopHoc;
    private Integer soMonHoc;
    
    // Trạng thái họ<PERSON> kỳ
    private String trangThaiHocKy; // "CHUA_BAT_DAU", "DANG_DIEN_RA", "DA_KET_THUC"
    private Integer soNgayConLai;
}
