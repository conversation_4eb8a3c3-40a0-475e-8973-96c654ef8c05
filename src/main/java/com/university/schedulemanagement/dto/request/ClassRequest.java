package com.university.schedulemanagement.dto.request;

import javax.validation.constraints.*;
import lombok.Data;

/**
 * ClassRequest - DTO tạo/cập nhật lớp học
 */
@Data
public class ClassRequest {

    @NotBlank(message = "Mã lớp không được để trống")
    private String maLop;

    @NotBlank(message = "Tên lớp không được để trống")
    private String tenLop;

    @NotNull(message = "Phải chọn khoa")
    private Long idPbmm;

    @NotNull(message = "<PERSON><PERSON>i chọn ngành học")
    private Long idNganhHoc;

    @NotNull(message = "<PERSON><PERSON><PERSON> chọn hệ đào tạo")
    private Long idHeDaoTao;

    @NotNull(message = "Kh<PERSON>a học không được để trống")
    @Min(value = 2020, message = "Khóa học phải từ 2020 trở lên")
    private Integer khoaHoc;

    @Min(value = 0, message = "<PERSON><PERSON> số không được âm")
    private Integer siSo = 0;

    private Long idCanBoChunhiem; // Giảng viên chủ nhiệm

    private String moTa;

    private Boolean trangThai = true;
}
