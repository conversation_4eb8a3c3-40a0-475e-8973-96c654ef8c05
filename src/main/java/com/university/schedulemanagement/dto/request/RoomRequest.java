package com.university.schedulemanagement.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;

/**
 * RoomRequest - DTO cho request tạo/cập nhật phòng học
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoomRequest {

    @NotBlank(message = "Mã phòng không được để trống")
    @Size(max = 20, message = "Mã phòng không được quá 20 ký tự")
    private String maPhong;

    @NotBlank(message = "Tên phòng không được để trống")
    @Size(max = 250, message = "Tên phòng không được quá 250 ký tự")
    private String tenPhong;

    @NotNull(message = "ID cơ sở không được để trống")
    private Long idCoSo;

    @NotBlank(message = "Loại phòng không được để trống")
    @Size(max = 50, message = "Loại phòng không được quá 50 ký tự")
    private String loaiPhong;

    @Min(value = 1, message = "Sức chứa phải lớn hơn 0")
    private Integer sucChua;

    private String tang;

    private String khu;

    private String moTa;

    private String trangBi;

    private Boolean trangThai = true;
}
