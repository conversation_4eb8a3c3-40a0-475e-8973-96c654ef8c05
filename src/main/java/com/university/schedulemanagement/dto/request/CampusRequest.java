package com.university.schedulemanagement.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * CampusRequest - DTO cho request tạo/cập nhật cơ sở
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CampusRequest {

    @NotBlank(message = "Mã cơ sở không được để trống")
    @Size(max = 10, message = "Mã cơ sở không được quá 10 ký tự")
    private String maCoSo;

    @NotBlank(message = "Tên cơ sở không được để trống")
    @Size(max = 250, message = "Tên cơ sở không được quá 250 ký tự")
    private String tenCoSo;

    @Size(max = 500, message = "Địa chỉ không được quá 500 ký tự")
    private String diaChi;

    private String soDienThoai;

    private String email;

    private String moTa;

    private Boolean trangThai = true;
}
