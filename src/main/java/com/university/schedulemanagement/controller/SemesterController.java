package com.university.schedulemanagement.controller;

import com.university.schedulemanagement.dto.request.SemesterRequest;
import com.university.schedulemanagement.dto.response.ApiResponse;
import com.university.schedulemanagement.dto.response.SemesterResponse;
import com.university.schedulemanagement.dto.response.PageResponse;
import com.university.schedulemanagement.service.SemesterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * SemesterController - Controller quản lý học kỳ
 */
@RestController
@RequestMapping("/api/semesters")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Semester Management", description = "API quản lý học kỳ")
public class SemesterController {

    private final SemesterService semesterService;

    @GetMapping
    @Operation(summary = "Lấy danh sách học kỳ", description = "Lấy danh sách tất cả học kỳ với phân trang")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<PageResponse<SemesterResponse>>> getAllSemesters(
            @RequestParam(required = false) Long academicYearId,
            Pageable pageable) {
        try {
            log.info("Getting all semesters for academic year: {}", academicYearId);
            PageResponse<SemesterResponse> response = semesterService.getAllSemesters(academicYearId, pageable);
            return ResponseEntity.ok(ApiResponse.success(response, "Lấy danh sách học kỳ thành công"));
        } catch (Exception e) {
            log.error("Error getting semesters: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy danh sách học kỳ: " + e.getMessage()));
        }
    }

    @GetMapping("/current")
    @Operation(summary = "Lấy học kỳ hiện tại", description = "Lấy thông tin học kỳ đang diễn ra")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA') or hasRole('GIANG_VIEN')")
    public ResponseEntity<ApiResponse<SemesterResponse>> getCurrentSemester() {
        try {
            log.info("Getting current semester");
            SemesterResponse response = semesterService.getCurrentSemester();
            return ResponseEntity.ok(ApiResponse.success(response, "Lấy học kỳ hiện tại thành công"));
        } catch (Exception e) {
            log.error("Error getting current semester: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy học kỳ hiện tại: " + e.getMessage()));
        }
    }

    @GetMapping("/academic-year/{academicYearId}")
    @Operation(summary = "Lấy học kỳ theo năm học", description = "Lấy danh sách học kỳ của một năm học")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA') or hasRole('GIANG_VIEN')")
    public ResponseEntity<ApiResponse<List<SemesterResponse>>> getSemestersByAcademicYear(@PathVariable Long academicYearId) {
        try {
            log.info("Getting semesters for academic year: {}", academicYearId);
            List<SemesterResponse> response = semesterService.getSemestersByAcademicYear(academicYearId);
            return ResponseEntity.ok(ApiResponse.success(response, "Lấy danh sách học kỳ theo năm học thành công"));
        } catch (Exception e) {
            log.error("Error getting semesters for academic year {}: {}", academicYearId, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy danh sách học kỳ: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Lấy thông tin học kỳ", description = "Lấy thông tin chi tiết của một học kỳ")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<SemesterResponse>> getSemester(@PathVariable Long id) {
        try {
            log.info("Getting semester with ID: {}", id);
            SemesterResponse response = semesterService.getSemesterById(id);
            return ResponseEntity.ok(ApiResponse.success(response, "Lấy thông tin học kỳ thành công"));
        } catch (Exception e) {
            log.error("Error getting semester {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy thông tin học kỳ: " + e.getMessage()));
        }
    }

    @PostMapping
    @Operation(summary = "Tạo học kỳ mới", description = "Tạo một học kỳ mới")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<SemesterResponse>> createSemester(@Valid @RequestBody SemesterRequest request) {
        try {
            log.info("Creating new semester: {}", request.getTenHocKy());
            SemesterResponse response = semesterService.createSemester(request);
            return ResponseEntity.ok(ApiResponse.success(response, "Tạo học kỳ thành công"));
        } catch (Exception e) {
            log.error("Error creating semester: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi tạo học kỳ: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "Cập nhật học kỳ", description = "Cập nhật thông tin học kỳ")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<SemesterResponse>> updateSemester(
            @PathVariable Long id, 
            @Valid @RequestBody SemesterRequest request) {
        try {
            log.info("Updating semester {}: {}", id, request.getTenHocKy());
            SemesterResponse response = semesterService.updateSemester(id, request);
            return ResponseEntity.ok(ApiResponse.success(response, "Cập nhật học kỳ thành công"));
        } catch (Exception e) {
            log.error("Error updating semester {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi cập nhật học kỳ: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Xóa học kỳ", description = "Xóa một học kỳ (chỉ khi chưa có lịch giảng)")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> deleteSemester(@PathVariable Long id) {
        try {
            log.info("Deleting semester with ID: {}", id);
            semesterService.deleteSemester(id);
            return ResponseEntity.ok(ApiResponse.success("Xóa học kỳ thành công"));
        } catch (Exception e) {
            log.error("Error deleting semester {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi xóa học kỳ: " + e.getMessage()));
        }
    }

    @PostMapping("/{id}/set-current")
    @Operation(summary = "Đặt học kỳ hiện tại", description = "Đặt học kỳ làm học kỳ hiện tại")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> setCurrentSemester(@PathVariable Long id) {
        try {
            log.info("Setting current semester to ID: {}", id);
            semesterService.setCurrentSemester(id);
            return ResponseEntity.ok(ApiResponse.success("Đặt học kỳ hiện tại thành công"));
        } catch (Exception e) {
            log.error("Error setting current semester {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi đặt học kỳ hiện tại: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}/statistics")
    @Operation(summary = "Thống kê học kỳ", description = "Lấy thống kê chi tiết của học kỳ")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<Object>> getSemesterStatistics(@PathVariable Long id) {
        try {
            log.info("Getting statistics for semester: {}", id);
            Object statistics = semesterService.getSemesterStatistics(id);
            return ResponseEntity.ok(ApiResponse.success(statistics, "Lấy thống kê học kỳ thành công"));
        } catch (Exception e) {
            log.error("Error getting semester statistics {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy thống kê học kỳ: " + e.getMessage()));
        }
    }
}
