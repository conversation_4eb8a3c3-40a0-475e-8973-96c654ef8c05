package com.university.schedulemanagement.controller;

import com.university.schedulemanagement.dto.request.*;
import com.university.schedulemanagement.dto.response.ApiResponse;
import com.university.schedulemanagement.dto.response.PageResponse;
import com.university.schedulemanagement.service.MasterDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * MasterDataController - Controller quản lý dữ liệu danh mục
 */
@RestController
@RequestMapping("/api/master-data")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Master Data Management", description = "API quản lý dữ liệu danh mục")
public class MasterDataController {

    private final MasterDataService masterDataService;

    // ==================== QUẢN LÝ KHOA/PHÒNG BAN ====================
    
    @GetMapping("/departments")
    @Operation(summary = "Lấy danh sách khoa", description = "Lấy danh sách tất cả khoa/phòng ban")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<PageResponse<Object>>> getAllDepartments(Pageable pageable) {
        try {
            log.info("Getting all departments");
            PageResponse<Object> response = masterDataService.getAllDepartments(pageable);
            return ResponseEntity.ok(ApiResponse.success(response, "Lấy danh sách khoa thành công"));
        } catch (Exception e) {
            log.error("Error getting departments: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy danh sách khoa: " + e.getMessage()));
        }
    }

    @PostMapping("/departments")
    @Operation(summary = "Tạo khoa mới", description = "Tạo khoa/phòng ban mới")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Object>> createDepartment(@Valid @RequestBody DepartmentRequest request) {
        try {
            log.info("Creating new department: {}", request.getTenKhoa());
            Object response = masterDataService.createDepartment(request);
            return ResponseEntity.ok(ApiResponse.success(response, "Tạo khoa thành công"));
        } catch (Exception e) {
            log.error("Error creating department: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi tạo khoa: " + e.getMessage()));
        }
    }

    // ==================== QUẢN LÝ MÔN HỌC ====================
    
    @GetMapping("/subjects")
    @Operation(summary = "Lấy danh sách môn học", description = "Lấy danh sách tất cả môn học")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<PageResponse<Object>>> getAllSubjects(
            @RequestParam(required = false) Long departmentId,
            Pageable pageable) {
        try {
            log.info("Getting all subjects for department: {}", departmentId);
            PageResponse<Object> response = masterDataService.getAllSubjects(departmentId, pageable);
            return ResponseEntity.ok(ApiResponse.success(response, "Lấy danh sách môn học thành công"));
        } catch (Exception e) {
            log.error("Error getting subjects: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy danh sách môn học: " + e.getMessage()));
        }
    }

    @PostMapping("/subjects")
    @Operation(summary = "Tạo môn học mới", description = "Tạo môn học mới")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Object>> createSubject(@Valid @RequestBody SubjectRequest request) {
        try {
            log.info("Creating new subject: {}", request.getTenMonHoc());
            Object response = masterDataService.createSubject(request);
            return ResponseEntity.ok(ApiResponse.success(response, "Tạo môn học thành công"));
        } catch (Exception e) {
            log.error("Error creating subject: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi tạo môn học: " + e.getMessage()));
        }
    }

    // ==================== QUẢN LÝ LỚP HỌC ====================
    
    @GetMapping("/classes")
    @Operation(summary = "Lấy danh sách lớp học", description = "Lấy danh sách tất cả lớp học")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<PageResponse<Object>>> getAllClasses(
            @RequestParam(required = false) Long departmentId,
            Pageable pageable) {
        try {
            log.info("Getting all classes for department: {}", departmentId);
            PageResponse<Object> response = masterDataService.getAllClasses(departmentId, pageable);
            return ResponseEntity.ok(ApiResponse.success(response, "Lấy danh sách lớp học thành công"));
        } catch (Exception e) {
            log.error("Error getting classes: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy danh sách lớp học: " + e.getMessage()));
        }
    }

    @PostMapping("/classes")
    @Operation(summary = "Tạo lớp học mới", description = "Tạo lớp học mới")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Object>> createClass(@Valid @RequestBody ClassRequest request) {
        try {
            log.info("Creating new class: {}", request.getTenLop());
            Object response = masterDataService.createClass(request);
            return ResponseEntity.ok(ApiResponse.success(response, "Tạo lớp học thành công"));
        } catch (Exception e) {
            log.error("Error creating class: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi tạo lớp học: " + e.getMessage()));
        }
    }

    // ==================== QUẢN LÝ PHÒNG HỌC ====================
    
    @GetMapping("/rooms")
    @Operation(summary = "Lấy danh sách phòng học", description = "Lấy danh sách tất cả phòng học")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<PageResponse<Object>>> getAllRooms(
            @RequestParam(required = false) Long campusId,
            Pageable pageable) {
        try {
            log.info("Getting all rooms for campus: {}", campusId);
            PageResponse<Object> response = masterDataService.getAllRooms(campusId, pageable);
            return ResponseEntity.ok(ApiResponse.success(response, "Lấy danh sách phòng học thành công"));
        } catch (Exception e) {
            log.error("Error getting rooms: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy danh sách phòng học: " + e.getMessage()));
        }
    }

    @PostMapping("/rooms")
    @Operation(summary = "Tạo phòng học mới", description = "Tạo phòng học mới")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Object>> createRoom(@Valid @RequestBody RoomRequest request) {
        try {
            log.info("Creating new room: {}", request.getTenPhong());
            Object response = masterDataService.createRoom(request);
            return ResponseEntity.ok(ApiResponse.success(response, "Tạo phòng học thành công"));
        } catch (Exception e) {
            log.error("Error creating room: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi tạo phòng học: " + e.getMessage()));
        }
    }

    // ==================== QUẢN LÝ CƠ SỞ ====================
    
    @GetMapping("/campuses")
    @Operation(summary = "Lấy danh sách cơ sở", description = "Lấy danh sách tất cả cơ sở")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<List<Object>>> getAllCampuses() {
        try {
            log.info("Getting all campuses");
            List<Object> response = masterDataService.getAllCampuses();
            return ResponseEntity.ok(ApiResponse.success(response, "Lấy danh sách cơ sở thành công"));
        } catch (Exception e) {
            log.error("Error getting campuses: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy danh sách cơ sở: " + e.getMessage()));
        }
    }

    @PostMapping("/campuses")
    @Operation(summary = "Tạo cơ sở mới", description = "Tạo cơ sở mới")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Object>> createCampus(@Valid @RequestBody CampusRequest request) {
        try {
            log.info("Creating new campus: {}", request.getTenCoSo());
            Object response = masterDataService.createCampus(request);
            return ResponseEntity.ok(ApiResponse.success(response, "Tạo cơ sở thành công"));
        } catch (Exception e) {
            log.error("Error creating campus: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi tạo cơ sở: " + e.getMessage()));
        }
    }

    // ==================== QUẢN LÝ GIẢNG VIÊN ====================
    
    @GetMapping("/teachers")
    @Operation(summary = "Lấy danh sách giảng viên", description = "Lấy danh sách tất cả giảng viên")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<PageResponse<Object>>> getAllTeachers(
            @RequestParam(required = false) Long departmentId,
            Pageable pageable) {
        try {
            log.info("Getting all teachers for department: {}", departmentId);
            PageResponse<Object> response = masterDataService.getAllTeachers(departmentId, pageable);
            return ResponseEntity.ok(ApiResponse.success(response, "Lấy danh sách giảng viên thành công"));
        } catch (Exception e) {
            log.error("Error getting teachers: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy danh sách giảng viên: " + e.getMessage()));
        }
    }

    @PostMapping("/teachers")
    @Operation(summary = "Tạo giảng viên mới", description = "Tạo tài khoản giảng viên mới")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Object>> createTeacher(@Valid @RequestBody TeacherRequest request) {
        try {
            log.info("Creating new teacher: {}", request.getTen());
            Object response = masterDataService.createTeacher(request);
            return ResponseEntity.ok(ApiResponse.success(response, "Tạo giảng viên thành công"));
        } catch (Exception e) {
            log.error("Error creating teacher: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi tạo giảng viên: " + e.getMessage()));
        }
    }

    // ==================== IMPORT DỮ LIỆU ====================
    
    @PostMapping("/import")
    @Operation(summary = "Import dữ liệu", description = "Import dữ liệu từ file Excel")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Object>> importData(
            @RequestParam String dataType,
            @RequestParam("file") org.springframework.web.multipart.MultipartFile file) {
        try {
            log.info("Importing data type: {}", dataType);
            Object response = masterDataService.importData(dataType, file);
            return ResponseEntity.ok(ApiResponse.success(response, "Import dữ liệu thành công"));
        } catch (Exception e) {
            log.error("Error importing data: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi import dữ liệu: " + e.getMessage()));
        }
    }
}
