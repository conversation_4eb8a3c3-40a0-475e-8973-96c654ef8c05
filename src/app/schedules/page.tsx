'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

import {
  Calendar,
  Plus,
  Search,
  Edit,
  Trash2,
  ArrowLeft,
  Clock,
  MapPin,
  User,
  ChevronLeft,
  ChevronRight,
  BookOpen
} from 'lucide-react'

interface Schedule {
  id: number
  tenMonHoc: string
  tenLop: string
  tenCanBo: string
  tenPhong: string
  tenBuoi: string
  tenHinhThuc: string
  ngayBatDau: string
  ngayKetThuc: string
  soTiet: number
  trangThai: string
}

export default function SchedulesPage() {
  const [schedules, setSchedules] = useState<Schedule[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [viewMode, setViewMode] = useState<'list' | 'calendar'>('list')
  const router = useRouter()

  useEffect(() => {
    // Kiểm tra token
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
      return
    }

    fetchSchedules()
  }, [router])

  const fetchSchedules = async () => {
    try {
      const token = localStorage.getItem('token')
      const response = await fetch('http://localhost:8080/api/schedules', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setSchedules(data.data)
        }
      }
    } catch (error) {
      console.error('Error fetching schedules:', error)
      // Mock data for demo
      setSchedules([
        {
          id: 1,
          tenMonHoc: 'Lập trình Java',
          tenLop: 'CNTT01',
          tenCanBo: 'Nguyễn Văn A',
          tenPhong: 'A101',
          tenBuoi: 'Sáng',
          tenHinhThuc: 'Lý thuyết',
          ngayBatDau: '2024-01-15',
          ngayKetThuc: '2024-05-15',
          soTiet: 45,
          trangThai: 'Đã duyệt'
        },
        {
          id: 2,
          tenMonHoc: 'Cơ sở dữ liệu',
          tenLop: 'CNTT02',
          tenCanBo: 'Trần Thị B',
          tenPhong: 'B201',
          tenBuoi: 'Chiều',
          tenHinhThuc: 'Thực hành',
          ngayBatDau: '2024-01-16',
          ngayKetThuc: '2024-05-16',
          soTiet: 30,
          trangThai: 'Chờ duyệt'
        }
      ])
    } finally {
      setLoading(false)
    }
  }

  const filteredSchedules = schedules.filter(schedule =>
    schedule.tenMonHoc.toLowerCase().includes(searchTerm.toLowerCase()) ||
    schedule.tenLop.toLowerCase().includes(searchTerm.toLowerCase()) ||
    schedule.tenCanBo.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Đã duyệt':
        return 'bg-green-100 text-green-800'
      case 'Chờ duyệt':
        return 'bg-yellow-100 text-yellow-800'
      case 'Từ chối':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/dashboard')}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Quay lại
              </Button>
              <Calendar className="h-6 w-6 text-blue-600 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900">
                Quản lý lịch giảng
              </h1>
            </div>
            <div className="flex space-x-2">
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                onClick={() => setViewMode('list')}
              >
                Danh sách
              </Button>
              <Button
                variant={viewMode === 'calendar' ? 'default' : 'outline'}
                onClick={() => setViewMode('calendar')}
              >
                Lịch
              </Button>
              <Button onClick={() => router.push('/schedules/create')}>
                <Plus className="h-4 w-4 mr-2" />
                Tạo lịch mới
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Tìm kiếm lịch giảng</CardTitle>
            <CardDescription>
              Tìm kiếm theo tên môn học, lớp hoặc giảng viên
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <Label htmlFor="search">Từ khóa tìm kiếm</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="search"
                    placeholder="Nhập tên môn học, lớp hoặc giảng viên..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Content based on view mode */}
        {viewMode === 'list' ? (
          /* Schedules List */
          <div className="grid gap-6">
            {filteredSchedules.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Không tìm thấy lịch giảng
                  </h3>
                  <p className="text-gray-500 mb-4">
                    Không có lịch giảng nào phù hợp với từ khóa tìm kiếm.
                  </p>
                  <Button onClick={() => router.push('/schedules/create')}>
                    <Plus className="h-4 w-4 mr-2" />
                    Tạo lịch mới
                  </Button>
                </CardContent>
              </Card>
            ) : (
              filteredSchedules.map((schedule) => (
                <Card key={schedule.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">{schedule.tenMonHoc}</CardTitle>
                        <CardDescription>Lớp: {schedule.tenLop}</CardDescription>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(schedule.trangThai)}`}>
                          {schedule.trangThai}
                        </span>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{schedule.tenCanBo}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{schedule.tenPhong}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{schedule.tenBuoi} - {schedule.tenHinhThuc}</span>
                      </div>
                    </div>
                    <div className="mt-4 pt-4 border-t">
                      <div className="flex justify-between items-center text-sm text-gray-600">
                        <span>
                          Thời gian: {new Date(schedule.ngayBatDau).toLocaleDateString('vi-VN')} - {new Date(schedule.ngayKetThuc).toLocaleDateString('vi-VN')}
                        </span>
                        <span>Số tiết: {schedule.soTiet}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        ) : (
          /* Calendar View */
          <CalendarView schedules={filteredSchedules} />
        )}
      </main>
    </div>
  )
}

// Calendar View Component
function CalendarView({ schedules }: { schedules: Schedule[] }) {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null)
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day))
    }

    return days
  }

  const getSchedulesForDate = (date: Date | null) => {
    if (!date) return []
    const dateStr = date.toISOString().split('T')[0]
    return schedules.filter(schedule =>
      schedule.ngayBatDau <= dateStr && schedule.ngayKetThuc >= dateStr
    )
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  const formatMonth = (date: Date) => {
    return date.toLocaleDateString('vi-VN', {
      month: 'long',
      year: 'numeric'
    })
  }

  const isToday = (date: Date | null) => {
    if (!date) return false
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }

  const isSelected = (date: Date | null) => {
    if (!date || !selectedDate) return false
    return date.toDateString() === selectedDate.toDateString()
  }

  const handleDateClick = (date: Date | null) => {
    if (!date) return
    setSelectedDate(date)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Đã duyệt':
        return 'bg-green-500'
      case 'Chờ duyệt':
        return 'bg-yellow-500'
      case 'Từ chối':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  const days = getDaysInMonth(currentDate)
  const weekDays = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7']

  return (
    <div className="space-y-4">
      {/* Calendar Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Lịch giảng toàn trường
            </CardTitle>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm" onClick={() => navigateMonth('prev')}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <h3 className="text-lg font-semibold capitalize">
                {formatMonth(currentDate)}
              </h3>
              <Button variant="outline" size="sm" onClick={() => navigateMonth('next')}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentDate(new Date())}
            >
              Hôm nay
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1">
            {/* Week day headers */}
            {weekDays.map(day => (
              <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                {day}
              </div>
            ))}

            {/* Calendar days */}
            {days.map((date, index) => {
              const daySchedules = getSchedulesForDate(date)

              return (
                <div
                  key={index}
                  className={`
                    min-h-[100px] p-1 border border-gray-200 cursor-pointer transition-colors
                    ${date ? 'hover:bg-gray-50' : ''}
                    ${isToday(date) ? 'bg-blue-50 border-blue-200' : ''}
                    ${isSelected(date) ? 'bg-blue-100 border-blue-300' : ''}
                    ${!date ? 'bg-gray-50' : ''}
                  `}
                  onClick={() => handleDateClick(date)}
                >
                  {date && (
                    <>
                      <div className={`
                        text-sm font-medium mb-1
                        ${isToday(date) ? 'text-blue-600' : 'text-gray-900'}
                      `}>
                        {date.getDate()}
                      </div>

                      {/* Schedules for this day */}
                      <div className="space-y-1">
                        {daySchedules.slice(0, 3).map(schedule => (
                          <div
                            key={schedule.id}
                            className={`
                              ${getStatusColor(schedule.trangThai)} text-white text-xs p-1 rounded cursor-pointer
                              hover:opacity-80 transition-opacity
                            `}
                          >
                            <div className="flex items-center space-x-1">
                              <BookOpen className="h-3 w-3" />
                              <span className="truncate">{schedule.tenMonHoc}</span>
                            </div>
                            <div className="text-xs opacity-90">
                              {schedule.tenLop}
                            </div>
                          </div>
                        ))}

                        {daySchedules.length > 3 && (
                          <div className="text-xs text-gray-500 text-center">
                            +{daySchedules.length - 3} khác
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Selected Date Schedules */}
      {selectedDate && (
        <Card>
          <CardHeader>
            <CardTitle>
              Lịch giảng ngày {selectedDate.toLocaleDateString('vi-VN')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {getSchedulesForDate(selectedDate).length === 0 ? (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 mb-4">
                    Không có lịch giảng nào trong ngày này
                  </p>
                </div>
              ) : (
                getSchedulesForDate(selectedDate).map(schedule => (
                  <div
                    key={schedule.id}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-semibold text-lg">{schedule.tenMonHoc}</h4>
                        <div className="mt-2 space-y-1 text-sm text-gray-600">
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-2" />
                            <span>GV: {schedule.tenCanBo}</span>
                          </div>
                          <div className="flex items-center">
                            <MapPin className="h-4 w-4 mr-2" />
                            <span>Phòng: {schedule.tenPhong}</span>
                          </div>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-2" />
                            <span>Buổi: {schedule.tenBuoi} - {schedule.tenHinhThuc}</span>
                          </div>
                        </div>
                      </div>
                      <div className={`${getStatusColor(schedule.trangThai)} text-white px-3 py-1 rounded-full text-sm`}>
                        {schedule.tenLop}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
