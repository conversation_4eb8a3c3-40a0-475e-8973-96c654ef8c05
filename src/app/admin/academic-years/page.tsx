"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Plus, Edit, Trash2, Calendar, CheckCircle } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface AcademicYear {
  idNienKhoa: number
  tenNienKhoa: string
  nam: number
  moTa?: string
  trangThai: boolean
  soHocKy: number
  ngayTao: string
  ngayCapNhat: string
}

interface AcademicYearForm {
  tenNienKhoa: string
  nam: number
  moTa: string
  trangThai: boolean
}

export default function AcademicYearsPage() {
  const [academicYears, setAcademicYears] = useState<AcademicYear[]>([])
  const [loading, setLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingYear, setEditingYear] = useState<AcademicYear | null>(null)
  const [formData, setFormData] = useState<AcademicYearForm>({
    tenNienKhoa: "",
    nam: new Date().getFullYear(),
    moTa: "",
    trangThai: true
  })

  useEffect(() => {
    fetchAcademicYears()
  }, [])

  const fetchAcademicYears = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/academic-years', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setAcademicYears(data.data.content || [])
      }
    } catch (error) {
      console.error('Error fetching academic years:', error)
      toast({
        title: "Lỗi",
        description: "Không thể tải danh sách năm học",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const url = editingYear 
        ? `/api/academic-years/${editingYear.idNienKhoa}`
        : '/api/academic-years'
      
      const method = editingYear ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(formData)
      })
      
      if (response.ok) {
        toast({
          title: "Thành công",
          description: editingYear ? "Cập nhật năm học thành công" : "Tạo năm học thành công"
        })
        setDialogOpen(false)
        resetForm()
        fetchAcademicYears()
      } else {
        const error = await response.json()
        toast({
          title: "Lỗi",
          description: error.message || "Có lỗi xảy ra",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Error saving academic year:', error)
      toast({
        title: "Lỗi",
        description: "Không thể lưu năm học",
        variant: "destructive"
      })
    }
  }

  const handleEdit = (year: AcademicYear) => {
    setEditingYear(year)
    setFormData({
      tenNienKhoa: year.tenNienKhoa,
      nam: year.nam,
      moTa: year.moTa || "",
      trangThai: year.trangThai
    })
    setDialogOpen(true)
  }

  const handleDelete = async (id: number) => {
    if (!confirm('Bạn có chắc chắn muốn xóa năm học này?')) {
      return
    }
    
    try {
      const response = await fetch(`/api/academic-years/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (response.ok) {
        toast({
          title: "Thành công",
          description: "Xóa năm học thành công"
        })
        fetchAcademicYears()
      } else {
        const error = await response.json()
        toast({
          title: "Lỗi",
          description: error.message || "Không thể xóa năm học",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Error deleting academic year:', error)
      toast({
        title: "Lỗi",
        description: "Không thể xóa năm học",
        variant: "destructive"
      })
    }
  }

  const handleActivate = async (id: number) => {
    try {
      const response = await fetch(`/api/academic-years/${id}/activate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (response.ok) {
        toast({
          title: "Thành công",
          description: "Kích hoạt năm học thành công"
        })
        fetchAcademicYears()
      }
    } catch (error) {
      console.error('Error activating academic year:', error)
      toast({
        title: "Lỗi",
        description: "Không thể kích hoạt năm học",
        variant: "destructive"
      })
    }
  }

  const resetForm = () => {
    setFormData({
      tenNienKhoa: "",
      nam: new Date().getFullYear(),
      moTa: "",
      trangThai: true
    })
    setEditingYear(null)
  }

  const handleDialogClose = () => {
    setDialogOpen(false)
    resetForm()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Quản Lý Năm Học</h1>
          <p className="text-muted-foreground">
            Tạo và quản lý các năm học/niên khóa
          </p>
        </div>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Thêm Năm Học
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>
                {editingYear ? 'Cập Nhật Năm Học' : 'Thêm Năm Học Mới'}
              </DialogTitle>
              <DialogDescription>
                {editingYear 
                  ? 'Cập nhật thông tin năm học' 
                  : 'Tạo một năm học/niên khóa mới'
                }
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="tenNienKhoa" className="text-right">
                    Tên Niên Khóa
                  </Label>
                  <Input
                    id="tenNienKhoa"
                    value={formData.tenNienKhoa}
                    onChange={(e) => setFormData({...formData, tenNienKhoa: e.target.value})}
                    className="col-span-3"
                    placeholder="VD: 2024-2025"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="nam" className="text-right">
                    Năm
                  </Label>
                  <Input
                    id="nam"
                    type="number"
                    value={formData.nam}
                    onChange={(e) => setFormData({...formData, nam: parseInt(e.target.value)})}
                    className="col-span-3"
                    min="2020"
                    max="2050"
                    required
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="moTa" className="text-right">
                    Mô Tả
                  </Label>
                  <Input
                    id="moTa"
                    value={formData.moTa}
                    onChange={(e) => setFormData({...formData, moTa: e.target.value})}
                    className="col-span-3"
                    placeholder="Mô tả năm học"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={handleDialogClose}>
                  Hủy
                </Button>
                <Button type="submit">
                  {editingYear ? 'Cập Nhật' : 'Tạo Mới'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Danh Sách Năm Học</CardTitle>
          <CardDescription>
            Quản lý tất cả các năm học trong hệ thống
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tên Niên Khóa</TableHead>
                <TableHead>Năm</TableHead>
                <TableHead>Số Học Kỳ</TableHead>
                <TableHead>Trạng Thái</TableHead>
                <TableHead>Ngày Tạo</TableHead>
                <TableHead className="text-right">Thao Tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {academicYears.map((year) => (
                <TableRow key={year.idNienKhoa}>
                  <TableCell className="font-medium">
                    {year.tenNienKhoa}
                  </TableCell>
                  <TableCell>{year.nam}</TableCell>
                  <TableCell>{year.soHocKy}</TableCell>
                  <TableCell>
                    <Badge variant={year.trangThai ? "default" : "secondary"}>
                      {year.trangThai ? "Hoạt động" : "Không hoạt động"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {new Date(year.ngayTao).toLocaleDateString('vi-VN')}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleActivate(year.idNienKhoa)}
                      >
                        <CheckCircle className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(year)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(year.idNienKhoa)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
