"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { 
  Users, 
  BookOpen, 
  GraduationCap, 
  Building, 
  Calendar,
  Database,
  Download,
  Upload,
  Settings,
  BarChart3
} from "lucide-react"

interface DashboardStats {
  totalTeachers: number
  totalSchedules: number
  totalSubjects: number
  totalClasses: number
  totalRooms: number
  totalDepartments: number
  currentSemester: string
  weeklySchedules: number
}

export default function AdminPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      // TODO: Replace with actual API call
      const response = await fetch('/api/admin/dashboard', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setStats(data.data)
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInitializeData = async () => {
    try {
      const response = await fetch('/api/admin/initialize-data', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (response.ok) {
        alert('Khởi tạo dữ liệu thành công!')
        fetchDashboardData()
      }
    } catch (error) {
      console.error('Error initializing data:', error)
      alert('Lỗi khi khởi tạo dữ liệu')
    }
  }

  const handleBackup = async () => {
    try {
      const response = await fetch('/api/admin/backup', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        alert(`Sao lưu thành công: ${data.data}`)
      }
    } catch (error) {
      console.error('Error creating backup:', error)
      alert('Lỗi khi tạo bản sao lưu')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Quản Trị Hệ Thống</h1>
          <p className="text-muted-foreground">
            Dashboard tổng quan và quản lý hệ thống
          </p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={handleBackup} variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Sao Lưu
          </Button>
          <Button onClick={handleInitializeData} variant="outline">
            <Database className="mr-2 h-4 w-4" />
            Khởi Tạo Dữ Liệu
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng Giảng Viên</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalTeachers || 0}</div>
            <p className="text-xs text-muted-foreground">
              Đang hoạt động
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng Lịch Giảng</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalSchedules || 0}</div>
            <p className="text-xs text-muted-foreground">
              Tuần này: {stats?.weeklySchedules || 0}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng Môn Học</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalSubjects || 0}</div>
            <p className="text-xs text-muted-foreground">
              Đang giảng dạy
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tổng Lớp Học</CardTitle>
            <GraduationCap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalClasses || 0}</div>
            <p className="text-xs text-muted-foreground">
              Đang hoạt động
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Current Semester Info */}
      {stats?.currentSemester && (
        <Card>
          <CardHeader>
            <CardTitle>Học Kỳ Hiện Tại</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Badge variant="default">{stats.currentSemester}</Badge>
              <span className="text-sm text-muted-foreground">
                {stats.weeklySchedules} lịch giảng trong tuần này
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Management Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Tổng Quan</TabsTrigger>
          <TabsTrigger value="academic">Năm Học & Học Kỳ</TabsTrigger>
          <TabsTrigger value="master-data">Dữ Liệu Danh Mục</TabsTrigger>
          <TabsTrigger value="import-export">Import/Export</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Thống Kê Hệ Thống</CardTitle>
                <CardDescription>
                  Tổng quan về tình trạng hoạt động
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span>Khoa/Phòng ban:</span>
                  <span className="font-medium">{stats?.totalDepartments || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span>Phòng học:</span>
                  <span className="font-medium">{stats?.totalRooms || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span>Môn học:</span>
                  <span className="font-medium">{stats?.totalSubjects || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span>Lớp học:</span>
                  <span className="font-medium">{stats?.totalClasses || 0}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Thao Tác Nhanh</CardTitle>
                <CardDescription>
                  Các chức năng quản trị thường dùng
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button className="w-full justify-start" variant="outline">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Xem Báo Cáo Thống Kê
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Settings className="mr-2 h-4 w-4" />
                  Cấu Hình Hệ Thống
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Building className="mr-2 h-4 w-4" />
                  Quản Lý Cơ Sở Vật Chất
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="academic" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Quản Lý Năm Học</CardTitle>
                <CardDescription>
                  Tạo và quản lý các năm học/niên khóa
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full">
                  Quản Lý Năm Học
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quản Lý Học Kỳ</CardTitle>
                <CardDescription>
                  Tạo và quản lý các học kỳ trong năm
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button className="w-full">
                  Quản Lý Học Kỳ
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="master-data" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Khoa/Phòng Ban</CardTitle>
              </CardHeader>
              <CardContent>
                <Button className="w-full">Quản Lý Khoa</Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Môn Học</CardTitle>
              </CardHeader>
              <CardContent>
                <Button className="w-full">Quản Lý Môn Học</Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Lớp Học</CardTitle>
              </CardHeader>
              <CardContent>
                <Button className="w-full">Quản Lý Lớp Học</Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Phòng Học</CardTitle>
              </CardHeader>
              <CardContent>
                <Button className="w-full">Quản Lý Phòng Học</Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Giảng Viên</CardTitle>
              </CardHeader>
              <CardContent>
                <Button className="w-full">Quản Lý Giảng Viên</Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Cơ Sở</CardTitle>
              </CardHeader>
              <CardContent>
                <Button className="w-full">Quản Lý Cơ Sở</Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="import-export" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Import Dữ Liệu</CardTitle>
                <CardDescription>
                  Nhập dữ liệu từ file Excel
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button className="w-full justify-start" variant="outline">
                  <Upload className="mr-2 h-4 w-4" />
                  Import Khoa
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Upload className="mr-2 h-4 w-4" />
                  Import Môn Học
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Upload className="mr-2 h-4 w-4" />
                  Import Lớp Học
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Upload className="mr-2 h-4 w-4" />
                  Import Giảng Viên
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Export Template</CardTitle>
                <CardDescription>
                  Tải template Excel để import
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button className="w-full justify-start" variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Template Khoa
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Template Môn Học
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Template Lớp Học
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Template Giảng Viên
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
