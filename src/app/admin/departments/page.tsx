"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Plus, Edit, Trash2, Building, Users, BookOpen, Upload, Download } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

interface Department {
  idPbmm: number
  maKhoa: string
  tenKhoa: string
  trangThai: boolean
  ngayTao: string
  ngayCapNhat: string
  soGiangVien?: number
  soMonHoc?: number
}

interface DepartmentForm {
  maKhoa: string
  tenKhoa: string
  moTa: string
  diaChi: string
  soDienThoai: string
  email: string
  trangThai: boolean
}

export default function DepartmentsPage() {
  const [departments, setDepartments] = useState<Department[]>([])
  const [loading, setLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingDepartment, setEditingDepartment] = useState<Department | null>(null)
  const [formData, setFormData] = useState<DepartmentForm>({
    maKhoa: "",
    tenKhoa: "",
    moTa: "",
    diaChi: "",
    soDienThoai: "",
    email: "",
    trangThai: true
  })

  useEffect(() => {
    fetchDepartments()
  }, [])

  const fetchDepartments = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/master-data/departments', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setDepartments(data.data.content || [])
      }
    } catch (error) {
      console.error('Error fetching departments:', error)
      toast({
        title: "Lỗi",
        description: "Không thể tải danh sách khoa",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const url = editingDepartment 
        ? `/api/master-data/departments/${editingDepartment.idPbmm}`
        : '/api/master-data/departments'
      
      const method = editingDepartment ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(formData)
      })
      
      if (response.ok) {
        toast({
          title: "Thành công",
          description: editingDepartment ? "Cập nhật khoa thành công" : "Tạo khoa thành công"
        })
        setDialogOpen(false)
        resetForm()
        fetchDepartments()
      } else {
        const error = await response.json()
        toast({
          title: "Lỗi",
          description: error.message || "Có lỗi xảy ra",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Error saving department:', error)
      toast({
        title: "Lỗi",
        description: "Không thể lưu khoa",
        variant: "destructive"
      })
    }
  }

  const handleEdit = (department: Department) => {
    setEditingDepartment(department)
    setFormData({
      maKhoa: department.maKhoa,
      tenKhoa: department.tenKhoa,
      moTa: "",
      diaChi: "",
      soDienThoai: "",
      email: "",
      trangThai: department.trangThai
    })
    setDialogOpen(true)
  }

  const handleDelete = async (id: number) => {
    if (!confirm('Bạn có chắc chắn muốn xóa khoa này?')) {
      return
    }
    
    try {
      const response = await fetch(`/api/master-data/departments/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      
      if (response.ok) {
        toast({
          title: "Thành công",
          description: "Xóa khoa thành công"
        })
        fetchDepartments()
      } else {
        const error = await response.json()
        toast({
          title: "Lỗi",
          description: error.message || "Không thể xóa khoa",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Error deleting department:', error)
      toast({
        title: "Lỗi",
        description: "Không thể xóa khoa",
        variant: "destructive"
      })
    }
  }

  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const formData = new FormData()
    formData.append('file', file)

    try {
      const response = await fetch('/api/master-data/import?dataType=department', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      })

      if (response.ok) {
        const result = await response.json()
        toast({
          title: "Thành công",
          description: `Import thành công ${result.data.savedRecords} khoa`
        })
        fetchDepartments()
      } else {
        const error = await response.json()
        toast({
          title: "Lỗi",
          description: error.message || "Lỗi khi import dữ liệu",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Error importing departments:', error)
      toast({
        title: "Lỗi",
        description: "Không thể import dữ liệu",
        variant: "destructive"
      })
    }

    // Reset input
    event.target.value = ''
  }

  const handleExportTemplate = async () => {
    try {
      const response = await fetch('/api/master-data/export-template?dataType=department', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'template_khoa.xlsx'
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('Error exporting template:', error)
      toast({
        title: "Lỗi",
        description: "Không thể tải template",
        variant: "destructive"
      })
    }
  }

  const resetForm = () => {
    setFormData({
      maKhoa: "",
      tenKhoa: "",
      moTa: "",
      diaChi: "",
      soDienThoai: "",
      email: "",
      trangThai: true
    })
    setEditingDepartment(null)
  }

  const handleDialogClose = () => {
    setDialogOpen(false)
    resetForm()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Quản Lý Khoa</h1>
          <p className="text-muted-foreground">
            Tạo và quản lý các khoa/phòng ban trong trường
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleExportTemplate}>
            <Download className="mr-2 h-4 w-4" />
            Template
          </Button>
          <Button variant="outline" asChild>
            <label htmlFor="import-file" className="cursor-pointer">
              <Upload className="mr-2 h-4 w-4" />
              Import
              <input
                id="import-file"
                type="file"
                accept=".xlsx,.xls"
                onChange={handleImport}
                className="hidden"
              />
            </label>
          </Button>
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => setDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Thêm Khoa
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>
                  {editingDepartment ? 'Cập Nhật Khoa' : 'Thêm Khoa Mới'}
                </DialogTitle>
                <DialogDescription>
                  {editingDepartment 
                    ? 'Cập nhật thông tin khoa/phòng ban' 
                    : 'Tạo một khoa/phòng ban mới'
                  }
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit}>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="maKhoa" className="text-right">
                      Mã Khoa
                    </Label>
                    <Input
                      id="maKhoa"
                      value={formData.maKhoa}
                      onChange={(e) => setFormData({...formData, maKhoa: e.target.value})}
                      className="col-span-3"
                      placeholder="VD: CNTT"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="tenKhoa" className="text-right">
                      Tên Khoa
                    </Label>
                    <Input
                      id="tenKhoa"
                      value={formData.tenKhoa}
                      onChange={(e) => setFormData({...formData, tenKhoa: e.target.value})}
                      className="col-span-3"
                      placeholder="VD: Công nghệ thông tin"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="email" className="text-right">
                      Email
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({...formData, email: e.target.value})}
                      className="col-span-3"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="soDienThoai" className="text-right">
                      Số Điện Thoại
                    </Label>
                    <Input
                      id="soDienThoai"
                      value={formData.soDienThoai}
                      onChange={(e) => setFormData({...formData, soDienThoai: e.target.value})}
                      className="col-span-3"
                      placeholder="028-12345678"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="diaChi" className="text-right">
                      Địa Chỉ
                    </Label>
                    <Textarea
                      id="diaChi"
                      value={formData.diaChi}
                      onChange={(e) => setFormData({...formData, diaChi: e.target.value})}
                      className="col-span-3"
                      placeholder="Địa chỉ khoa"
                      rows={2}
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="moTa" className="text-right">
                      Mô Tả
                    </Label>
                    <Textarea
                      id="moTa"
                      value={formData.moTa}
                      onChange={(e) => setFormData({...formData, moTa: e.target.value})}
                      className="col-span-3"
                      placeholder="Mô tả về khoa"
                      rows={3}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="button" variant="outline" onClick={handleDialogClose}>
                    Hủy
                  </Button>
                  <Button type="submit">
                    {editingDepartment ? 'Cập Nhật' : 'Tạo Mới'}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Danh Sách Khoa</CardTitle>
          <CardDescription>
            Quản lý tất cả các khoa/phòng ban trong trường
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Mã Khoa</TableHead>
                <TableHead>Tên Khoa</TableHead>
                <TableHead>Giảng Viên</TableHead>
                <TableHead>Môn Học</TableHead>
                <TableHead>Trạng Thái</TableHead>
                <TableHead>Ngày Tạo</TableHead>
                <TableHead className="text-right">Thao Tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {departments.map((department) => (
                <TableRow key={department.idPbmm}>
                  <TableCell className="font-medium">
                    <div className="flex items-center space-x-2">
                      <Building className="h-4 w-4 text-muted-foreground" />
                      <span>{department.maKhoa}</span>
                    </div>
                  </TableCell>
                  <TableCell>{department.tenKhoa}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span>{department.soGiangVien || 0}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      <BookOpen className="h-4 w-4 text-muted-foreground" />
                      <span>{department.soMonHoc || 0}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={department.trangThai ? "default" : "secondary"}>
                      {department.trangThai ? "Hoạt động" : "Không hoạt động"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {new Date(department.ngayTao).toLocaleDateString('vi-VN')}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(department)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(department.idPbmm)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
