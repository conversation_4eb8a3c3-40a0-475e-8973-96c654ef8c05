# 🚀 Backend - Schedule Management API

## 📋 Tổng Quan

Backend API cho hệ thống quản lý lịch giảng, đ<PERSON><PERSON><PERSON> xây dựng bằng Spring Boot với các tính năng:

- ✅ RESTful API với JWT Authentication
- ✅ Quản lý lịch giảng theo quy trình 7 bước
- ✅ Tự động tính toán và tổng hợp giờ giảng
- ✅ Phân quyền theo vai trò
- ✅ Swagger UI documentation
- ✅ Xuất báo cáo Excel

## 🚀 Công Nghệ

- **Framework**: Spring Boot 2.7.18
- **Database**: MySQL 8.0
- **Authentication**: JWT (JSON Web Token)
- **Documentation**: Swagger/OpenAPI 3
- **Build Tool**: Maven
- **Java Version**: 8+

## 📦 Cài Đặt và Chạy

### 1. Y<PERSON>u <PERSON>ố<PERSON>
- Java 8 hoặc cao hơn
- Maven 3.6+
- MySQL 8.0+

### 2. C<PERSON><PERSON> Database
```sql
CREATE DATABASE schedule_management;
CREATE USER 'app_user'@'localhost' IDENTIFIED BY 'app_password';
GRANT ALL PRIVILEGES ON schedule_management.* TO 'app_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. Chạy Ứng Dụng
```bash
# Từ thư mục backend
mvn spring-boot:run

# Hoặc build và chạy jar
mvn clean package
java -jar target/schedule-management-*.jar
```

### 4. Chạy với Docker
```bash
# Build image
docker build -t schedule-backend .

# Chạy container
docker run -p 8080:8080 schedule-backend
```

## 🔗 API Endpoints

### Base URL
```
http://localhost:8080/api
```

### Documentation
- **Swagger UI**: http://localhost:8080/api/swagger-ui.html
- **API Docs**: http://localhost:8080/api/api-docs

### Health Check
```bash
curl http://localhost:8080/api/health
```

## 🔐 Authentication

### Đăng Nhập
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"maCanBo":"admin","matKhau":"123456"}'
```

### Sử Dụng Token
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:8080/api/auth/me
```

## 👥 Tài Khoản Mặc Định

| Vai Trò | Username | Password | Quyền |
|---------|----------|----------|-------|
| Admin | `admin` | `123456` | Toàn quyền |
| Trưởng Khoa | `truongkhoa` | `123456` | Quản lý khoa |
| Giảng Viên | `giangvien` | `123456` | Xem cá nhân |

## 🏗️ Cấu Trúc Code

```
src/
├── main/
│   ├── java/
│   │   └── com/university/schedule/
│   │       ├── config/          # Cấu hình Spring
│   │       ├── controller/      # REST Controllers
│   │       ├── dto/            # Data Transfer Objects
│   │       ├── entity/         # JPA Entities
│   │       ├── repository/     # Data Access Layer
│   │       ├── service/        # Business Logic
│   │       └── util/           # Utilities
│   └── resources/
│       ├── application.yml     # Cấu hình ứng dụng
│       └── db/migration/       # Database scripts
└── test/                       # Unit tests
```

## 🧪 Testing

```bash
# Chạy tất cả tests
mvn test

# Chạy với coverage
mvn test jacoco:report
```

## 📚 Tài Liệu

- [API Usage Guide](../API_USAGE_GUIDE.md)
- [API Examples](../API_EXAMPLES.md)
- Swagger UI: http://localhost:8080/api/swagger-ui.html
