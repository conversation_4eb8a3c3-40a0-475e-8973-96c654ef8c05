package com.university.schedulemanagement.controller;

import com.university.schedulemanagement.dto.response.ApiResponse;
import com.university.schedulemanagement.dto.response.DashboardResponse;
import com.university.schedulemanagement.service.AdminService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * AdminController - Controller quản trị hệ thống
 */
@RestController
@RequestMapping("/api/admin")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Admin Management", description = "API quản trị hệ thống")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {

    private final AdminService adminService;

    @GetMapping("/dashboard")
    @Operation(summary = "Dashboard quản trị", description = "<PERSON><PERSON><PERSON> thông tin tổng quan hệ thống")
    public ResponseEntity<ApiResponse<DashboardResponse>> getDashboard() {
        try {
            log.info("Getting admin dashboard data");
            DashboardResponse dashboard = adminService.getDashboardData();
            return ResponseEntity.ok(ApiResponse.success(dashboard, "Lấy dữ liệu dashboard thành công"));
        } catch (Exception e) {
            log.error("Error getting dashboard data: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy dữ liệu dashboard: " + e.getMessage()));
        }
    }

    @GetMapping("/system-info")
    @Operation(summary = "Thông tin hệ thống", description = "Lấy thông tin chi tiết về hệ thống")
    public ResponseEntity<ApiResponse<Object>> getSystemInfo() {
        try {
            log.info("Getting system information");
            Object systemInfo = adminService.getSystemInfo();
            return ResponseEntity.ok(ApiResponse.success(systemInfo, "Lấy thông tin hệ thống thành công"));
        } catch (Exception e) {
            log.error("Error getting system info: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy thông tin hệ thống: " + e.getMessage()));
        }
    }

    @PostMapping("/initialize-data")
    @Operation(summary = "Khởi tạo dữ liệu", description = "Khởi tạo lại dữ liệu mẫu cho hệ thống")
    public ResponseEntity<ApiResponse<String>> initializeData() {
        try {
            log.info("Initializing system data");
            adminService.initializeSystemData();
            return ResponseEntity.ok(ApiResponse.success("Khởi tạo dữ liệu thành công"));
        } catch (Exception e) {
            log.error("Error initializing data: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi khởi tạo dữ liệu: " + e.getMessage()));
        }
    }

    @PostMapping("/backup")
    @Operation(summary = "Sao lưu dữ liệu", description = "Tạo bản sao lưu dữ liệu hệ thống")
    public ResponseEntity<ApiResponse<String>> backupData() {
        try {
            log.info("Creating system backup");
            String backupPath = adminService.createBackup();
            return ResponseEntity.ok(ApiResponse.success(backupPath, "Sao lưu dữ liệu thành công"));
        } catch (Exception e) {
            log.error("Error creating backup: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi sao lưu dữ liệu: " + e.getMessage()));
        }
    }

    @GetMapping("/statistics")
    @Operation(summary = "Thống kê hệ thống", description = "Lấy các thống kê tổng quan của hệ thống")
    public ResponseEntity<ApiResponse<Object>> getStatistics() {
        try {
            log.info("Getting system statistics");
            Object statistics = adminService.getSystemStatistics();
            return ResponseEntity.ok(ApiResponse.success(statistics, "Lấy thống kê thành công"));
        } catch (Exception e) {
            log.error("Error getting statistics: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy thống kê: " + e.getMessage()));
        }
    }
}
