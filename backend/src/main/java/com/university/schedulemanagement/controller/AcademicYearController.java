package com.university.schedulemanagement.controller;

import com.university.schedulemanagement.dto.request.AcademicYearRequest;
import com.university.schedulemanagement.dto.response.ApiResponse;
import com.university.schedulemanagement.dto.response.AcademicYearResponse;
import com.university.schedulemanagement.dto.response.PageResponse;
import com.university.schedulemanagement.service.AcademicYearService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * AcademicYearController - Controller quản lý năm học/niên khóa
 */
@RestController
@RequestMapping("/api/academic-years")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Academic Year Management", description = "API quản lý năm học/niên khóa")
public class AcademicYearController {

    private final AcademicYearService academicYearService;

    @GetMapping
    @Operation(summary = "Lấy danh sách năm học", description = "Lấy danh sách tất cả năm học với phân trang")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<PageResponse<AcademicYearResponse>>> getAllAcademicYears(Pageable pageable) {
        try {
            log.info("Getting all academic years with pagination");
            PageResponse<AcademicYearResponse> response = academicYearService.getAllAcademicYears(pageable);
            return ResponseEntity.ok(ApiResponse.success(response, "Lấy danh sách năm học thành công"));
        } catch (Exception e) {
            log.error("Error getting academic years: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy danh sách năm học: " + e.getMessage()));
        }
    }

    @GetMapping("/active")
    @Operation(summary = "Lấy danh sách năm học đang hoạt động", description = "Lấy danh sách năm học đang hoạt động")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA') or hasRole('GIANG_VIEN')")
    public ResponseEntity<ApiResponse<List<AcademicYearResponse>>> getActiveAcademicYears() {
        try {
            log.info("Getting active academic years");
            List<AcademicYearResponse> response = academicYearService.getActiveAcademicYears();
            return ResponseEntity.ok(ApiResponse.success(response, "Lấy danh sách năm học hoạt động thành công"));
        } catch (Exception e) {
            log.error("Error getting active academic years: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy danh sách năm học hoạt động: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Lấy thông tin năm học", description = "Lấy thông tin chi tiết của một năm học")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TRUONG_KHOA')")
    public ResponseEntity<ApiResponse<AcademicYearResponse>> getAcademicYear(@PathVariable Long id) {
        try {
            log.info("Getting academic year with ID: {}", id);
            AcademicYearResponse response = academicYearService.getAcademicYearById(id);
            return ResponseEntity.ok(ApiResponse.success(response, "Lấy thông tin năm học thành công"));
        } catch (Exception e) {
            log.error("Error getting academic year {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi lấy thông tin năm học: " + e.getMessage()));
        }
    }

    @PostMapping
    @Operation(summary = "Tạo năm học mới", description = "Tạo một năm học/niên khóa mới")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<AcademicYearResponse>> createAcademicYear(@Valid @RequestBody AcademicYearRequest request) {
        try {
            log.info("Creating new academic year: {}", request.getTenNienKhoa());
            AcademicYearResponse response = academicYearService.createAcademicYear(request);
            return ResponseEntity.ok(ApiResponse.success(response, "Tạo năm học thành công"));
        } catch (Exception e) {
            log.error("Error creating academic year: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi tạo năm học: " + e.getMessage()));
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "Cập nhật năm học", description = "Cập nhật thông tin năm học")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<AcademicYearResponse>> updateAcademicYear(
            @PathVariable Long id, 
            @Valid @RequestBody AcademicYearRequest request) {
        try {
            log.info("Updating academic year {}: {}", id, request.getTenNienKhoa());
            AcademicYearResponse response = academicYearService.updateAcademicYear(id, request);
            return ResponseEntity.ok(ApiResponse.success(response, "Cập nhật năm học thành công"));
        } catch (Exception e) {
            log.error("Error updating academic year {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi cập nhật năm học: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Xóa năm học", description = "Xóa một năm học (chỉ khi chưa có dữ liệu liên quan)")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> deleteAcademicYear(@PathVariable Long id) {
        try {
            log.info("Deleting academic year with ID: {}", id);
            academicYearService.deleteAcademicYear(id);
            return ResponseEntity.ok(ApiResponse.success("Xóa năm học thành công"));
        } catch (Exception e) {
            log.error("Error deleting academic year {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi xóa năm học: " + e.getMessage()));
        }
    }

    @PostMapping("/{id}/activate")
    @Operation(summary = "Kích hoạt năm học", description = "Đặt năm học làm năm học hiện tại")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<String>> activateAcademicYear(@PathVariable Long id) {
        try {
            log.info("Activating academic year with ID: {}", id);
            academicYearService.activateAcademicYear(id);
            return ResponseEntity.ok(ApiResponse.success("Kích hoạt năm học thành công"));
        } catch (Exception e) {
            log.error("Error activating academic year {}: {}", id, e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Lỗi khi kích hoạt năm học: " + e.getMessage()));
        }
    }
}
