package com.university.schedulemanagement.constant;

/**
 * SecurityConstants - <PERSON><PERSON><PERSON> hằng số bảo mật
 */
public final class SecurityConstants {

    private SecurityConstants() {}

    public static final String TOKEN_PREFIX = "Bearer ";
    public static final String HEADER_AUTHORIZATION = "Authorization";
    public static final String AUTHORITIES_KEY = "auth";

    // Role names
    public static final String ROLE_ADMIN = "ADMIN";
    public static final String ROLE_TRUONG_KHOA = "TRUONG_KHOA";
    public static final String ROLE_GIANG_VIEN = "GIANG_VIEN";

    // Permission strings
    public static final String AUTHORITY_ADMIN = "hasRole('" + ROLE_ADMIN + "')";
    public static final String AUTHORITY_TRUONG_KHOA = "hasRole('" + ROLE_TRUONG_KHOA + "')";
    public static final String AUTHORITY_GIANG_VIEN = "hasRole('" + ROLE_GIANG_VIEN + "')";
    public static final String AUTHORITY_ADMIN_OR_TRUONG_KHOA =
            "hasRole('" + ROLE_ADMIN + "') or hasRole('" + ROLE_TRUONG_KHOA + "')";
    public static final String AUTHORITY_ALL_ROLES =
            "hasRole('" + ROLE_ADMIN + "') or hasRole('" + ROLE_TRUONG_KHOA + "') or hasRole('" + ROLE_GIANG_VIEN + "')";

    // Default passwords
    public static final String DEFAULT_PASSWORD = "123456";
    public static final int MIN_PASSWORD_LENGTH = 6;
    public static final int MAX_PASSWORD_LENGTH = 50;
}
