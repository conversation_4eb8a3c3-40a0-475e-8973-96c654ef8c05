package com.university.schedulemanagement.constant;

/**
 * MessageConstants - <PERSON><PERSON><PERSON> thông báo của hệ thống
 */
public final class MessageConstants {

    private MessageConstants() {}

    // Success messages
    public static final String SUCCESS_CREATE = "Tạo mới thành công";
    public static final String SUCCESS_UPDATE = "Cập nhật thành công";
    public static final String SUCCESS_DELETE = "Xóa thành công";
    public static final String SUCCESS_LOGIN = "Đăng nhập thành công";
    public static final String SUCCESS_LOGOUT = "Đăng xuất thành công";
    public static final String SUCCESS_CHANGE_PASSWORD = "Đổi mật khẩu thành công";
    public static final String SUCCESS_RESET_PASSWORD = "Reset mật khẩu thành công";
    public static final String SUCCESS_EXPORT = "Xuất file thành công";

    // Error messages
    public static final String ERROR_NOT_FOUND = "Không tìm thấy dữ liệu";
    public static final String ERROR_DUPLICATE = "Dữ liệu đã tồn tại";
    public static final String ERROR_INVALID_INPUT = "Dữ liệu đầu vào không hợp lệ";
    public static final String ERROR_UNAUTHORIZED = "Không có quyền truy cập";
    public static final String ERROR_FORBIDDEN = "Bị cấm truy cập";
    public static final String ERROR_INTERNAL_SERVER = "Lỗi hệ thống";
    public static final String ERROR_LOGIN_FAILED = "Đăng nhập thất bại";
    public static final String ERROR_INVALID_TOKEN = "Token không hợp lệ";
    public static final String ERROR_EXPIRED_TOKEN = "Token đã hết hạn";
    public static final String ERROR_WRONG_PASSWORD = "Mật khẩu không đúng";
    public static final String ERROR_PASSWORD_MISMATCH = "Mật khẩu xác nhận không khớp";

    // Schedule messages
    public static final String ERROR_SCHEDULE_CONFLICT = "Có xung đột lịch giảng";
    public static final String ERROR_ROOM_OCCUPIED = "Phòng học đã có lịch";
    public static final String ERROR_TEACHER_BUSY = "Giảng viên đã có lịch";
    public static final String ERROR_INVALID_TIME_SLOT = "Khung giờ không hợp lệ";
    public static final String ERROR_ROOM_NOT_SUITABLE = "Phòng học không phù hợp";

    // Teaching hours messages
    public static final String INFO_CALCULATING_HOURS = "Đang tính toán giờ giảng...";
    public static final String SUCCESS_CALCULATE_HOURS = "Tính toán giờ giảng thành công";
    public static final String ERROR_CALCULATE_HOURS = "Lỗi khi tính toán giờ giảng";

    // Export messages
    public static final String INFO_EXPORTING = "Đang xuất file...";
    public static final String ERROR_EXPORT_FAILED = "Xuất file thất bại";
    public static final String ERROR_NO_DATA_TO_EXPORT = "Không có dữ liệu để xuất";
}
