package com.university.schedulemanagement.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;
import java.time.LocalDate;

/**
 * HinhThucHoc Entity - H<PERSON><PERSON> thức học
 */
@Entity
@Table(name = "HINH_THUC_HOC")
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
public class HinhThucHoc {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_HINH_THUC")
    private Long idHinhThuc;

    @Column(name = "TEN_HINH_THUC", nullable = false, length = 50)
    private String tenHinhThuc; // LT, TH

    @Column(name = "MO_TA", length = 250)
    private String moTa;

    @OneToMany(mappedBy = "hinhThucHoc", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<LichGiang> lichGiangList;
}
