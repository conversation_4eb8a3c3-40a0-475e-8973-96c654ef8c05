package com.university.schedulemanagement.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;
import java.time.LocalDate;

/**
 * HocKy Entity - <PERSON><PERSON><PERSON>
 */
@Entity
@Table(name = "HOC_KY")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class HocKy extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_HOC_KY")
    private Long idHocKy;

    @Column(name = "ID_NIEN_KHOA", nullable = false)
    private Long idNienKhoa;

    @Column(name = "TEN_HOC_KY", nullable = false, length = 100)
    private String tenHocKy;

    @Column(name = "SO_TUAN")
    private Integer soTuan;

    @Column(name = "NGAY_BAT_DAU")
    private LocalDate ngayBatDau;

    @Column(name = "NGAY_KET_THUC")
    private LocalDate ngayKetThuc;

    @Column(name = "HIEN_TAI")
    private Boolean hienTai = false;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_NIEN_KHOA", insertable = false, updatable = false)
    private NienKhoa nienKhoa;

    @OneToMany(mappedBy = "hocKy", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<LichGiang> lichGiangList;
}
