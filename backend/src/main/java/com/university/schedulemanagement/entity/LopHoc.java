package com.university.schedulemanagement.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;

/**
 * LopHoc Entity - Lớp học
 */
@Entity
@Table(name = "LOP_HOC")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class LopHoc extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_LOP")
    private Long idLop;

    @Column(name = "MA_LOP", unique = true, nullable = false, length = 20)
    private String maLop;

    @Column(name = "TEN_LOP", nullable = false, length = 250)
    private String tenLop;

    @Column(name = "ID_NGANH", nullable = false)
    private Long idNganh;

    @Column(name = "ID_HE_DAO_TAO", nullable = false)
    private Long idHeDaoTao;

    @Column(name = "SI_SO")
    private Integer siSo = 0;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_NGANH", insertable = false, updatable = false)
    private NganhHoc nganhHoc;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_HE_DAO_TAO", insertable = false, updatable = false)
    private HeDaoTao heDaoTao;

    @OneToMany(mappedBy = "lopHoc", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<LichGiang> lichGiangList;

    public String getLopInfo() {
        return String.format("%s - %s (%s)", maLop, tenLop,
                nganhHoc != null ? nganhHoc.getTenNganh() : "");
    }
}
