package com.university.schedulemanagement.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;

/**
 * MonHoc Entity - Môn học
 */
@Entity
@Table(name = "MON_HOC")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class MonHoc extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_MON_HOC")
    private Long idMonHoc;

    @Column(name = "ID_LOAI_MON_HOC", nullable = false)
    private Long idLoaiMonHoc;

    @Column(name = "ID_PBMM", nullable = false)
    private Long idPbmm;

    @Column(name = "ID_NHOM_LK")
    private Long idNhomLk;

    @Column(name = "ID_HE_DAO_TAO", nullable = false)
    private Long idHeDaoTao;

    @Column(name = "MA_MON_HOC", unique = true, nullable = false, length = 10)
    private String maMonHoc;

    @Column(name = "TEN_MON_HOC", nullable = false, length = 250)
    private String tenMonHoc;

    @Column(name = "SO_TIET_LT")
    private Integer soTietLt = 0;

    @Column(name = "SO_TIET_TH")
    private Integer soTietTh = 0;

    @Column(name = "SO_TIET_TU")
    private Integer soTietTu = 0;

    @Column(name = "MON_DIEU_KIEN")
    private Boolean monDieuKien = false;

    @Column(name = "MON_TN")
    private Boolean monTn = false;

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_LOAI_MON_HOC", insertable = false, updatable = false)
    private LoaiMonHoc loaiMonHoc;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_PBMM", insertable = false, updatable = false)
    private PBMM pbmm;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_NHOM_LK", insertable = false, updatable = false)
    private NhomLK nhomLk;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_HE_DAO_TAO", insertable = false, updatable = false)
    private HeDaoTao heDaoTao;

    @OneToMany(mappedBy = "monHoc", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<Nhom> nhomList;

    @OneToMany(mappedBy = "monHoc", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<LichGiang> lichGiangList;

    // Helper methods
    public String getFullInfo() {
        return String.format("%s - %s", maMonHoc, tenMonHoc);
    }

    public Integer getTongSoTiet() {
        return (soTietLt != null ? soTietLt : 0) +
                (soTietTh != null ? soTietTh : 0) +
                (soTietTu != null ? soTietTu : 0);
    }

    public Boolean hasThucHanh() {
        return soTietTh != null && soTietTh > 0;
    }

    public Boolean hasLyThuyet() {
        return soTietLt != null && soTietLt > 0;
    }
}
