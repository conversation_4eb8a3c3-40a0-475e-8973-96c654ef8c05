package com.university.schedulemanagement.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;
/**
 * NienKhoa Entity - Niên khóa
 */
@Entity
@Table(name = "NIEN_KHOA")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class NienKhoa extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_NIEN_KHOA")
    private Long idNienKhoa;

    @Column(name = "TEN_NIEN_KHOA", nullable = false, length = 250)
    private String tenNienKhoa;

    @Column(name = "NAM", nullable = false)
    private Integer nam;

    @Column(name = "ID_THONG_TU")
    private Long idThongTu;

    @OneToMany(mappedBy = "nienKhoa", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<HocKy> hocKyList;
}
