package com.university.schedulemanagement.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;

/**
 * NganhHoc Entity - Ngành học
 */
@Entity
@Table(name = "NGANH_HOC")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class NganhHoc extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_NGANH")
    private Long idNganh;

    @Column(name = "MA_NGANH", unique = true, nullable = false, length = 10)
    private String maNganh;

    @Column(name = "TEN_NGANH", nullable = false, length = 250)
    private String tenNganh;

    @Column(name = "ID_PBMM", nullable = false)
    private Long idPbmm;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_PBMM", insertable = false, updatable = false)
    private PBMM pbmm;

    @OneToMany(mappedBy = "nganhHoc", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<LopHoc> lopHocList;
}
