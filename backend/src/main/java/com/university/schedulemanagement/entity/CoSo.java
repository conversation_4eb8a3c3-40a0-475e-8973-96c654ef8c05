package com.university.schedulemanagement.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.util.List;


/**
 * CoSo Entity - Cơ sở
 */
@Entity
@Table(name = "CO_SO")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class CoSo extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_CO_SO")
    private Long idCoSo;

    @Column(name = "MA_CO_SO", nullable = false, length = 10)
    private String maCoSo; // CS1, CS2

    @Column(name = "TEN_CO_SO", nullable = false, length = 250)
    private String tenCoSo;

    @Column(name = "DIA_CHI", length = 500)
    private String diaChi;

    @OneToMany(mappedBy = "coSo", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<PhongHoc> phongHocList;
}
