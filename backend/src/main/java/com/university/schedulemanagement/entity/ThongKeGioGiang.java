package com.university.schedulemanagement.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * ThongKeGioGiang Entity - Thống kê giờ giảng
 */
@Entity
@Table(name = "THONG_KE_GIO_GIANG",
        uniqueConstraints = @UniqueConstraint(columnNames = {"ID_CAN_BO", "ID_HOC_KY"}))
@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
public class ThongKeGioGiang {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_THONG_KE")
    private Long idThongKe;

    @Column(name = "ID_CAN_BO", nullable = false)
    private Long idCanBo;

    @Column(name = "ID_HOC_KY", nullable = false)
    private Long idHocKy;

    @Column(name = "TONG_GIO_LT", precision = 8, scale = 2)
    private BigDecimal tongGioLt = BigDecimal.ZERO;

    @Column(name = "TONG_GIO_TH", precision = 8, scale = 2)
    private BigDecimal tongGioTh = BigDecimal.ZERO;

    @Column(name = "TONG_GIO_QUYDOI", precision = 8, scale = 2)
    private BigDecimal tongGioQuyDoi = BigDecimal.ZERO; // Tổng giờ đã quy đổi theo hệ số

    @Column(name = "NGAY_TINH")
    private LocalDateTime ngayTinh = LocalDateTime.now();

    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_CAN_BO", insertable = false, updatable = false)
    private CanBo canBo;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ID_HOC_KY", insertable = false, updatable = false)
    private HocKy hocKy;

    // Helper methods
    public BigDecimal getTongGioTong() {
        BigDecimal lt = tongGioLt != null ? tongGioLt : BigDecimal.ZERO;
        BigDecimal th = tongGioTh != null ? tongGioTh : BigDecimal.ZERO;
        return lt.add(th);
    }

    public String getThongKeInfo() {
        return String.format("GV: %s - HK: %s - Tổng: %.2f giờ",
                canBo != null ? canBo.getTen() : "",
                hocKy != null ? hocKy.getTenHocKy() : "",
                getTongGioTong()
        );
    }

    public void updateTongGio() {
        this.tongGioQuyDoi = getTongGioTong();
        this.ngayTinh = LocalDateTime.now();
    }

    // Tính tỷ lệ phân bố giờ LT/TH
    public String getTyLePhanBo() {
        BigDecimal tong = getTongGioTong();
        if (tong.compareTo(BigDecimal.ZERO) == 0) {
            return "0% LT - 0% TH";
        }

        BigDecimal lt = tongGioLt != null ? tongGioLt : BigDecimal.ZERO;
        BigDecimal th = tongGioTh != null ? tongGioTh : BigDecimal.ZERO;

        double phanTramLt = lt.divide(tong, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(100)).doubleValue();
        double phanTramTh = th.divide(tong, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(100)).doubleValue();

        return String.format("%.1f%% LT - %.1f%% TH", phanTramLt, phanTramTh);
    }
}
