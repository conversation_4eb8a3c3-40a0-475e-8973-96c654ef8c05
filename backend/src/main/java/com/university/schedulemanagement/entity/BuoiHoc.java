package com.university.schedulemanagement.entity;

import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalTime;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * BuoiHoc Entity - Buổi học
 */
@Entity
@Table(name = "BUOI_HOC")
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class BuoiHoc extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID_BUOI")
    private Long idBuoi;

    @Column(name = "TEN_BUOI", nullable = false, length = 20)
    private String tenBuoi; // Sáng, Chiều, Tối

    @Column(name = "GIO_BAT_DAU", nullable = false)
    private LocalTime gioBatDau;

    @Column(name = "GIO_KET_THUC", nullable = false)
    private LocalTime gioKetThuc;

    @OneToMany(mappedBy = "buoiHoc", fetch = FetchType.LAZY)
    @JsonIgnore
    private List<LichGiang> lichGiangList;

    public String getBuoiInfo() {
        return String.format("%s (%s - %s)", tenBuoi, gioBatDau, gioKetThuc);
    }
}
