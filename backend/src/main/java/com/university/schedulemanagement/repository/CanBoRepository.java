package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * CanBoRepository - Repository cho Giảng viên/Cán bộ
 */
@Repository
public interface CanBoRepository extends JpaRepository<CanBo, Long> {

    Optional<CanBo> findByMaCanBo(String maCanBo);

    Optional<CanBo> findByMaCanBoAndTrangThaiTrue(String maCanBo);

    List<CanBo> findByIdPbmmAndTrangThaiTrue(Long idPbmm);

    List<CanBo> findByIdVaiTroAndTrangThaiTrue(Long idVaiTro);

    @Query("SELECT cb FROM CanBo cb WHERE cb.trangThai = true AND " +
            "(LOWER(cb.ten) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "LOWER(cb.maCanBo) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<CanBo> findByKeyword(@Param("keyword") String keyword, Pageable pageable);

    @Query("SELECT COUNT(cb) FROM CanBo cb WHERE cb.idPbmm = :idPbmm AND cb.trangThai = true")
    Long countByKhoa(@Param("idPbmm") Long idPbmm);

    @Query("SELECT COUNT(cb) FROM CanBo cb WHERE cb.idPbmm = :idPbmm")
    Long countByIdPbmm(@Param("idPbmm") Long idPbmm);

    @Query("SELECT COUNT(cb) FROM CanBo cb WHERE cb.trangThai = true")
    Long countByTrangThaiTrue();

    @Query("SELECT COUNT(cb) FROM CanBo cb JOIN cb.vaiTro vt WHERE vt.tenVaiTro = :tenVaiTro")
    Long countByVaiTroTenVaiTro(@Param("tenVaiTro") String tenVaiTro);
}
