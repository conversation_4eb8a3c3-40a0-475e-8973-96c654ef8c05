package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * HocKyRepository - Repository cho Học kỳ
 */
@Repository
public interface HocKyRepository extends JpaRepository<HocKy, Long> {

    Optional<HocKy> findByHienTaiTrue();

    List<HocKy> findByIdNienKhoa(Long idNienKhoa);

    @Query("SELECT hk FROM HocKy hk ORDER BY hk.ngayBatDau DESC")
    List<HocKy> findAllOrderByDateDesc();

    @Query("SELECT hk FROM HocKy hk WHERE hk.ngayBatDau <= :currentDate AND hk.ngayKetThuc >= :currentDate")
    List<HocKy> findActiveHocKy(@Param("currentDate") LocalDate currentDate);

    @Query("SELECT hk FROM HocKy hk WHERE hk.trangThai = true ORDER BY hk.ngayBatDau DESC")
    List<HocKy> findByTrangThaiTrueOrderByNgayBatDauDesc();

    @Query("SELECT hk FROM HocKy hk WHERE hk.idNienKhoa = :idNienKhoa AND hk.trangThai = true ORDER BY hk.ngayBatDau")
    List<HocKy> findByIdNienKhoaAndTrangThaiTrueOrderByNgayBatDau(@Param("idNienKhoa") Long idNienKhoa);

    @Query("SELECT COUNT(hk) FROM HocKy hk WHERE hk.trangThai = true")
    Long countByTrangThaiTrue();

    @Query("SELECT COUNT(hk) FROM HocKy hk WHERE hk.idNienKhoa = :idNienKhoa")
    Long countByIdNienKhoa(@Param("idNienKhoa") Long idNienKhoa);

    @Query("SELECT hk FROM HocKy hk WHERE " +
            "(LOWER(hk.tenHocKy) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<HocKy> findByKeyword(@Param("keyword") String keyword, Pageable pageable);

    @Query("SELECT hk FROM HocKy hk WHERE hk.idNienKhoa = :idNienKhoa AND " +
            "(LOWER(hk.tenHocKy) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<HocKy> findByIdNienKhoaAndKeyword(@Param("idNienKhoa") Long idNienKhoa,
                                           @Param("keyword") String keyword,
                                           Pageable pageable);

    boolean existsByTenHocKyAndIdNienKhoa(String tenHocKy, Long idNienKhoa);

    @Modifying
    @Query("UPDATE HocKy hk SET hk.hienTai = false WHERE hk.hienTai = true")
    void clearCurrentSemester();
}
