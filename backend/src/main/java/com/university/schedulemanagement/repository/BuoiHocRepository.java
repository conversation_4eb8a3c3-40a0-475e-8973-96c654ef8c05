package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * BuoiHocRepository - Repository cho Buổi học
 */
@Repository
public interface BuoiHocRepository extends JpaRepository<BuoiHoc, Long> {

    Optional<BuoiHoc> findByTenBuoi(String tenBuoi);

    @Query("SELECT bh FROM BuoiHoc bh ORDER BY bh.gioBatDau")
    List<BuoiHoc> findAllOrderByTime();
}






