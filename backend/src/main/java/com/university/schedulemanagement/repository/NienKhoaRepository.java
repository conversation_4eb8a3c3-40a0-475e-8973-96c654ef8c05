package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;


/**
 * NienKhoaRepository - Repository cho <PERSON> kho<PERSON>
 */
@Repository
public interface NienKhoaRepository extends JpaRepository<NienKhoa, Long> {

    List<NienKhoa> findByNam(Integer nam);

    @Query("SELECT nk FROM NienKhoa nk ORDER BY nk.nam DESC")
    List<NienKhoa> findAllOrderByYearDesc();
}
