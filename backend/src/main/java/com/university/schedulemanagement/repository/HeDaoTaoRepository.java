package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
/**
 * HeDaoTaoRepository - Repository cho Hệ đào tạo
 */
@Repository
public interface HeDaoTaoRepository extends JpaRepository<HeDaoTao, Long> {

    Optional<HeDaoTao> findByTenHeDaoTao(String tenHeDaoTao);

    @Query("SELECT hdt FROM HeDaoTao hdt WHERE " +
            "LOWER(hdt.tenHeDaoTao) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    List<HeDaoTao> findByKeyword(@Param("keyword") String keyword);
}
