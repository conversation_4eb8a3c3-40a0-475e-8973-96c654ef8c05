package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * NhomLKRepository - Repository cho Nhóm liên kết
 */
@Repository
public interface NhomLKRepository extends JpaRepository<NhomLK, Long> {

    Optional<NhomLK> findByTenNhomLk(String tenNhomLk);

    @Query("SELECT nlk FROM NhomLK nlk WHERE " +
            "LOWER(nlk.tenNhomLk) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    List<NhomLK> findByKeyword(@Param("keyword") String keyword);
}
