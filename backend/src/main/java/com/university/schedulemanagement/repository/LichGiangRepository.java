package com.university.schedulemanagement.repository;

import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * LichGiangRepository - Repository cho Lịch giảng
 */
@Repository
public interface LichGiangRepository extends JpaRepository<LichGiang, Long> {

    List<LichGiang> findByIdCanBoAndTrangThaiTrue(Long idCanBo);

    List<LichGiang> findByIdHocKyAndTrangThaiTrue(Long idHocKy);

    List<LichGiang> findByIdLopAndTrangThaiTrue(Long idLop);

    List<LichGiang> findByIdMonHocAndTrangThaiTrue(Long idMonHoc);

    @Query("SELECT lg FROM LichGiang lg WHERE lg.idCanBo = :idCanBo AND lg.idHocKy = :idHocKy AND lg.trangThai = true")
    List<LichGiang> findByCanBoAndHocKy(@Param("idCanBo") Long idCanBo, @Param("idHocKy") Long idHocKy);

    @Query("SELECT lg FROM LichGiang lg WHERE lg.idPhong = :idPhong AND lg.thuHoc = :thuHoc AND " +
            "lg.idBuoi = :idBuoi AND lg.idHocKy = :idHocKy AND lg.trangThai = true")
    List<LichGiang> findConflictSchedule(@Param("idPhong") Long idPhong,
                                         @Param("thuHoc") Integer thuHoc,
                                         @Param("idBuoi") Long idBuoi,
                                         @Param("idHocKy") Long idHocKy);

    @Query("SELECT lg FROM LichGiang lg WHERE lg.idCanBo = :idCanBo AND lg.thuHoc = :thuHoc AND " +
            "lg.idBuoi = :idBuoi AND lg.idHocKy = :idHocKy AND lg.trangThai = true")
    List<LichGiang> findTeacherConflictSchedule(@Param("idCanBo") Long idCanBo,
                                                @Param("thuHoc") Integer thuHoc,
                                                @Param("idBuoi") Long idBuoi,
                                                @Param("idHocKy") Long idHocKy);

    @Query("SELECT lg FROM LichGiang lg JOIN lg.hocKy hk JOIN hk.nienKhoa nk " +
            "WHERE lg.idCanBo = :idCanBo AND hk.ngayBatDau >= :fromDate AND hk.ngayKetThuc <= :toDate " +
            "AND lg.trangThai = true ORDER BY hk.ngayBatDau")
    List<LichGiang> findTeachingHoursByPeriod(@Param("idCanBo") Long idCanBo,
                                              @Param("fromDate") LocalDate fromDate,
                                              @Param("toDate") LocalDate toDate);

    // ==================== THỐNG KÊ METHODS ====================

    @Query("SELECT COUNT(lg) FROM LichGiang lg WHERE lg.idHocKy = :idHocKy AND lg.trangThai = true")
    Long countByIdHocKyAndTrangThaiTrue(@Param("idHocKy") Long idHocKy);

    @Query("SELECT COUNT(DISTINCT lg.idCanBo) FROM LichGiang lg WHERE lg.idHocKy = :idHocKy AND lg.trangThai = true")
    Long countDistinctTeachersByHocKy(@Param("idHocKy") Long idHocKy);

    @Query("SELECT COUNT(DISTINCT lg.idMonHoc) FROM LichGiang lg WHERE lg.idHocKy = :idHocKy AND lg.trangThai = true")
    Long countDistinctSubjectsByHocKy(@Param("idHocKy") Long idHocKy);

    @Query("SELECT COUNT(DISTINCT lg.idLop) FROM LichGiang lg WHERE lg.idHocKy = :idHocKy AND lg.trangThai = true")
    Long countDistinctClassesByHocKy(@Param("idHocKy") Long idHocKy);

    @Query("SELECT COUNT(lg) FROM LichGiang lg WHERE lg.trangThai = true")
    Long countByTrangThaiTrue();

    @Query("SELECT COUNT(lg) FROM LichGiang lg WHERE lg.trangThai = true AND " +
            "WEEK(lg.ngayTao) = WEEK(CURRENT_DATE) AND YEAR(lg.ngayTao) = YEAR(CURRENT_DATE)")
    Long countSchedulesThisWeek();

    @Query("SELECT COUNT(lg) FROM LichGiang lg WHERE lg.trangThai = true AND " +
            "MONTH(lg.ngayTao) = MONTH(CURRENT_DATE) AND YEAR(lg.ngayTao) = YEAR(CURRENT_DATE)")
    Long countSchedulesThisMonth();

    @Query("SELECT SUM(lg.soTiet * lg.heSo) FROM LichGiang lg WHERE lg.trangThai = true")
    BigDecimal sumTotalTeachingHours();
}
