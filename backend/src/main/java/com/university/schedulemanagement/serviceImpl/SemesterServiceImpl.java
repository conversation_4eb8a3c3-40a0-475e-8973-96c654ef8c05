package com.university.schedulemanagement.serviceImpl;

import com.university.schedulemanagement.dto.request.SemesterRequest;
import com.university.schedulemanagement.dto.response.SemesterResponse;
import com.university.schedulemanagement.dto.response.PageResponse;
import com.university.schedulemanagement.entity.HocKy;
import com.university.schedulemanagement.entity.NienKhoa;
import com.university.schedulemanagement.exception.BadRequestException;
import com.university.schedulemanagement.exception.ResourceNotFoundException;
import com.university.schedulemanagement.repository.HocKyRepository;
import com.university.schedulemanagement.repository.NienKhoaRepository;
import com.university.schedulemanagement.repository.LichGiangRepository;
import com.university.schedulemanagement.service.SemesterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * SemesterServiceImpl - Implementation của SemesterService
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class SemesterServiceImpl implements SemesterService {

    private final HocKyRepository hocKyRepository;
    private final NienKhoaRepository nienKhoaRepository;
    private final LichGiangRepository lichGiangRepository;

    @Override
    @Transactional(readOnly = true)
    public PageResponse<SemesterResponse> getAllSemesters(Long academicYearId, Pageable pageable) {
        log.info("Getting all semesters for academic year: {}", academicYearId);
        
        Page<HocKy> hocKyPage;
        
        if (academicYearId != null) {
            hocKyPage = hocKyRepository.findByIdNienKhoaAndKeyword(academicYearId, "", pageable);
        } else {
            hocKyPage = hocKyRepository.findAll(pageable);
        }
        
        List<SemesterResponse> responses = hocKyPage.getContent().stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
        
        return PageResponse.<SemesterResponse>builder()
                .content(responses)
                .page(hocKyPage.getNumber())
                .size(hocKyPage.getSize())
                .totalElements(hocKyPage.getTotalElements())
                .totalPages(hocKyPage.getTotalPages())
                .first(hocKyPage.isFirst())
                .last(hocKyPage.isLast())
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public SemesterResponse getCurrentSemester() {
        log.info("Getting current semester");
        
        HocKy currentSemester = hocKyRepository.findByHienTaiTrue()
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy học kỳ hiện tại"));
        
        return mapToResponseWithDetails(currentSemester);
    }

    @Override
    @Transactional(readOnly = true)
    public List<SemesterResponse> getSemestersByAcademicYear(Long academicYearId) {
        log.info("Getting semesters for academic year: {}", academicYearId);
        
        // Kiểm tra năm học tồn tại
        nienKhoaRepository.findById(academicYearId)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy năm học với ID: " + academicYearId));
        
        List<HocKy> hocKyList = hocKyRepository.findByIdNienKhoaAndTrangThaiTrueOrderByNgayBatDau(academicYearId);
        
        return hocKyList.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public SemesterResponse getSemesterById(Long id) {
        log.info("Getting semester with ID: {}", id);
        
        HocKy hocKy = hocKyRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy học kỳ với ID: " + id));
        
        return mapToResponseWithDetails(hocKy);
    }

    @Override
    public SemesterResponse createSemester(SemesterRequest request) {
        log.info("Creating new semester: {}", request.getTenHocKy());
        
        // Validate dữ liệu
        validateSemesterRequest(request, null);
        
        // Tạo entity mới
        HocKy hocKy = new HocKy();
        mapRequestToEntity(request, hocKy);
        
        // Nếu đặt làm học kỳ hiện tại, clear các học kỳ hiện tại khác
        if (Boolean.TRUE.equals(request.getHienTai())) {
            hocKyRepository.clearCurrentSemester();
        }
        
        // Lưu vào database
        hocKy = hocKyRepository.save(hocKy);
        
        log.info("Semester created successfully with ID: {}", hocKy.getIdHocKy());
        return mapToResponse(hocKy);
    }

    @Override
    public SemesterResponse updateSemester(Long id, SemesterRequest request) {
        log.info("Updating semester {}: {}", id, request.getTenHocKy());
        
        // Kiểm tra tồn tại
        HocKy existingHocKy = hocKyRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy học kỳ với ID: " + id));
        
        // Validate dữ liệu
        validateSemesterRequest(request, id);
        
        // Nếu đặt làm học kỳ hiện tại, clear các học kỳ hiện tại khác
        if (Boolean.TRUE.equals(request.getHienTai()) && !Boolean.TRUE.equals(existingHocKy.getHienTai())) {
            hocKyRepository.clearCurrentSemester();
        }
        
        // Cập nhật thông tin
        mapRequestToEntity(request, existingHocKy);
        
        // Lưu vào database
        existingHocKy = hocKyRepository.save(existingHocKy);
        
        log.info("Semester updated successfully: {}", id);
        return mapToResponse(existingHocKy);
    }

    @Override
    public void deleteSemester(Long id) {
        log.info("Deleting semester with ID: {}", id);
        
        // Kiểm tra tồn tại
        HocKy hocKy = hocKyRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy học kỳ với ID: " + id));
        
        // Kiểm tra có thể xóa không
        if (!canDeleteSemester(id)) {
            throw new BadRequestException("Không thể xóa học kỳ này vì đã có lịch giảng");
        }
        
        // Xóa học kỳ
        hocKyRepository.delete(hocKy);
        
        log.info("Semester deleted successfully: {}", id);
    }

    @Override
    public void setCurrentSemester(Long id) {
        log.info("Setting current semester to ID: {}", id);
        
        // Kiểm tra tồn tại
        HocKy hocKy = hocKyRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy học kỳ với ID: " + id));
        
        // Clear học kỳ hiện tại
        hocKyRepository.clearCurrentSemester();
        
        // Đặt học kỳ mới
        hocKy.setHienTai(true);
        hocKyRepository.save(hocKy);
        
        log.info("Current semester set successfully: {}", id);
    }

    @Override
    @Transactional(readOnly = true)
    public Object getSemesterStatistics(Long id) {
        log.info("Getting statistics for semester: {}", id);
        
        // Kiểm tra tồn tại
        HocKy hocKy = hocKyRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy học kỳ với ID: " + id));
        
        Map<String, Object> statistics = new HashMap<>();
        
        // Thống kê lịch giảng
        Long scheduleCount = lichGiangRepository.countByIdHocKyAndTrangThaiTrue(id);
        statistics.put("totalSchedules", scheduleCount);
        
        // Thống kê giảng viên
        Long teacherCount = lichGiangRepository.countDistinctTeachersByHocKy(id);
        statistics.put("totalTeachers", teacherCount);
        
        // Thống kê môn học
        Long subjectCount = lichGiangRepository.countDistinctSubjectsByHocKy(id);
        statistics.put("totalSubjects", subjectCount);
        
        // Thống kê lớp học
        Long classCount = lichGiangRepository.countDistinctClassesByHocKy(id);
        statistics.put("totalClasses", classCount);
        
        statistics.put("semester", mapToResponse(hocKy));
        
        return statistics;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean canDeleteSemester(Long id) {
        log.info("Checking if semester {} can be deleted", id);
        
        // Kiểm tra có lịch giảng nào không
        Long scheduleCount = lichGiangRepository.countByIdHocKyAndTrangThaiTrue(id);
        
        return scheduleCount == 0;
    }

    @Override
    @Transactional(readOnly = true)
    public List<SemesterResponse> getActiveSemesters() {
        log.info("Getting active semesters");
        
        List<HocKy> activeSemesters = hocKyRepository.findByTrangThaiTrueOrderByNgayBatDauDesc();
        
        return activeSemesters.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public void updateSemesterStatus() {
        log.info("Updating semester status automatically");
        
        LocalDate currentDate = LocalDate.now();
        List<HocKy> allSemesters = hocKyRepository.findAll();
        
        for (HocKy hocKy : allSemesters) {
            String newStatus = calculateSemesterStatus(hocKy, currentDate);
            // Có thể lưu status vào database nếu cần
        }
        
        log.info("Semester status updated successfully");
    }

    // ==================== PRIVATE METHODS ====================
    
    private void validateSemesterRequest(SemesterRequest request, Long excludeId) {
        // Kiểm tra năm học tồn tại
        nienKhoaRepository.findById(request.getIdNienKhoa())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy năm học với ID: " + request.getIdNienKhoa()));
        
        // Kiểm tra trùng tên học kỳ trong cùng năm học
        if (hocKyRepository.existsByTenHocKyAndIdNienKhoa(request.getTenHocKy(), request.getIdNienKhoa())) {
            if (excludeId == null) {
                throw new BadRequestException("Tên học kỳ đã tồn tại trong năm học này: " + request.getTenHocKy());
            }
            // Thêm logic kiểm tra cho update nếu cần
        }
        
        // Kiểm tra ngày bắt đầu < ngày kết thúc
        if (request.getNgayBatDau().isAfter(request.getNgayKetThuc())) {
            throw new BadRequestException("Ngày bắt đầu phải trước ngày kết thúc");
        }
    }

    private void mapRequestToEntity(SemesterRequest request, HocKy hocKy) {
        hocKy.setIdNienKhoa(request.getIdNienKhoa());
        hocKy.setTenHocKy(request.getTenHocKy());
        hocKy.setSoTuan(request.getSoTuan());
        hocKy.setNgayBatDau(request.getNgayBatDau());
        hocKy.setNgayKetThuc(request.getNgayKetThuc());
        hocKy.setHienTai(request.getHienTai());
        hocKy.setTrangThai(request.getTrangThai());
    }

    private SemesterResponse mapToResponse(HocKy hocKy) {
        SemesterResponse response = new SemesterResponse();
        response.setIdHocKy(hocKy.getIdHocKy());
        response.setIdNienKhoa(hocKy.getIdNienKhoa());
        response.setTenHocKy(hocKy.getTenHocKy());
        response.setSoTuan(hocKy.getSoTuan());
        response.setNgayBatDau(hocKy.getNgayBatDau());
        response.setNgayKetThuc(hocKy.getNgayKetThuc());
        response.setHienTai(hocKy.getHienTai());
        response.setTrangThai(hocKy.getTrangThai());
        response.setNgayTao(hocKy.getNgayTao());
        response.setNgayCapNhat(hocKy.getNgayCapNhat());
        
        // Thông tin niên khóa
        if (hocKy.getNienKhoa() != null) {
            response.setTenNienKhoa(hocKy.getNienKhoa().getTenNienKhoa());
            response.setNamNienKhoa(hocKy.getNienKhoa().getNam());
        }
        
        // Tính toán trạng thái và số ngày còn lại
        LocalDate currentDate = LocalDate.now();
        response.setTrangThaiHocKy(calculateSemesterStatus(hocKy, currentDate));
        response.setSoNgayConLai(calculateDaysRemaining(hocKy, currentDate));
        
        return response;
    }

    private SemesterResponse mapToResponseWithDetails(HocKy hocKy) {
        SemesterResponse response = mapToResponse(hocKy);
        
        // Thêm thống kê chi tiết
        Long scheduleCount = lichGiangRepository.countByIdHocKyAndTrangThaiTrue(hocKy.getIdHocKy());
        response.setSoLichGiang(scheduleCount.intValue());
        
        return response;
    }

    private String calculateSemesterStatus(HocKy hocKy, LocalDate currentDate) {
        if (currentDate.isBefore(hocKy.getNgayBatDau())) {
            return "CHUA_BAT_DAU";
        } else if (currentDate.isAfter(hocKy.getNgayKetThuc())) {
            return "DA_KET_THUC";
        } else {
            return "DANG_DIEN_RA";
        }
    }

    private Integer calculateDaysRemaining(HocKy hocKy, LocalDate currentDate) {
        if (currentDate.isAfter(hocKy.getNgayKetThuc())) {
            return 0;
        }
        return (int) ChronoUnit.DAYS.between(currentDate, hocKy.getNgayKetThuc());
    }
}
