package com.university.schedulemanagement.serviceImpl;

import com.university.schedulemanagement.dto.request.TeacherRequest;
import com.university.schedulemanagement.entity.*;
import com.university.schedulemanagement.exception.BadRequestException;
import com.university.schedulemanagement.exception.ResourceNotFoundException;
import com.university.schedulemanagement.repository.*;
import com.university.schedulemanagement.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * TeacherServiceImpl - Implementation của TeacherService
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class TeacherServiceImpl implements TeacherService {

    private final CanBoRepository canBoRepository;
    private final VaiTroRepository vaiTroRepository;
    private final PBMMRepository pbmmRepository;
    private final AuthService authService;
    private final PasswordEncoder passwordEncoder;

    @Override
    public CanBo createTeacher(TeacherRequest request) {
        log.info("Creating teacher: {}", request.getMaCanBo());

        // Chỉ admin mới có thể tạo giảng viên
        if (!authService.isAdmin()) {
            throw new BadRequestException("Chỉ admin mới có thể tạo tài khoản giảng viên");
        }

        // Kiểm tra trùng mã cán bộ
        if (canBoRepository.findByMaCanBo(request.getMaCanBo()).isPresent()) {
            throw new BadRequestException("Mã cán bộ đã tồn tại: " + request.getMaCanBo());
        }

        // Validate related entities
        vaiTroRepository.findById(request.getIdVaiTro())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy vai trò với ID: " + request.getIdVaiTro()));

        pbmmRepository.findById(request.getIdPbmm())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy khoa với ID: " + request.getIdPbmm()));

        // Tạo entity
        CanBo canBo = new CanBo();
        mapTeacherRequestToEntity(request, canBo);

        // Set mật khẩu mặc định
        canBo.setMatKhau(passwordEncoder.encode("123456"));

        canBo = canBoRepository.save(canBo);
        log.info("Teacher created successfully: {}", canBo.getMaCanBo());

        return canBo;
    }

    @Override
    public CanBo updateTeacher(Long id, TeacherRequest request) {
        log.info("Updating teacher ID: {}", id);

        CanBo existingTeacher = canBoRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy giảng viên với ID: " + id));

        // Kiểm tra quyền
        if (!authService.isAdmin()) {
            throw new BadRequestException("Chỉ admin mới có thể sửa thông tin giảng viên");
        }

        // Kiểm tra trùng mã cán bộ (nếu thay đổi)
        if (!existingTeacher.getMaCanBo().equals(request.getMaCanBo())) {
            if (canBoRepository.findByMaCanBo(request.getMaCanBo()).isPresent()) {
                throw new BadRequestException("Mã cán bộ đã tồn tại: " + request.getMaCanBo());
            }
        }

        // Validate related entities
        vaiTroRepository.findById(request.getIdVaiTro())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy vai trò với ID: " + request.getIdVaiTro()));

        pbmmRepository.findById(request.getIdPbmm())
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy khoa với ID: " + request.getIdPbmm()));

        // Update entity (không thay đổi mật khẩu)
        String oldPassword = existingTeacher.getMatKhau();
        mapTeacherRequestToEntity(request, existingTeacher);
        existingTeacher.setMatKhau(oldPassword);

        existingTeacher = canBoRepository.save(existingTeacher);
        log.info("Teacher updated successfully: {}", existingTeacher.getMaCanBo());

        return existingTeacher;
    }

    @Override
    public void deleteTeacher(Long id) {
        log.info("Deleting teacher ID: {}", id);

        CanBo teacher = canBoRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy giảng viên với ID: " + id));

        // Chỉ admin mới có thể xóa
        if (!authService.isAdmin()) {
            throw new BadRequestException("Chỉ admin mới có thể xóa giảng viên");
        }

        // Kiểm tra giảng viên có đang dạy không
        if (teacher.getLichGiangList() != null && !teacher.getLichGiangList().isEmpty()) {
            throw new BadRequestException("Không thể xóa giảng viên đang có lịch giảng");
        }

        // Soft delete - chỉ deactive
        teacher.setTrangThai(false);
        canBoRepository.save(teacher);

        log.info("Teacher deactivated successfully: {}", teacher.getMaCanBo());
    }

    @Override
    @Transactional(readOnly = true)
    public CanBo getTeacherById(Long id) {
        return canBoRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy giảng viên với ID: " + id));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CanBo> getAllTeachers(Pageable pageable) {
        return canBoRepository.findAll(pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CanBo> searchTeachers(String keyword, Pageable pageable) {
        return canBoRepository.findByKeyword(keyword, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<CanBo> getTeachersByDepartment(Long departmentId) {
        if (!authService.canAccessDepartmentData(departmentId)) {
            throw new BadRequestException("Bạn không có quyền xem giảng viên của khoa này");
        }
        return canBoRepository.findByIdPbmmAndTrangThaiTrue(departmentId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<CanBo> getTeachersByRole(Long roleId) {
        return canBoRepository.findByIdVaiTroAndTrangThaiTrue(roleId);
    }

    @Override
    public void updateTeacherStatus(Long id, Boolean status) {
        log.info("Updating teacher status ID: {}, status: {}", id, status);

        if (!authService.isAdmin()) {
            throw new BadRequestException("Chỉ admin mới có thể thay đổi trạng thái giảng viên");
        }

        CanBo teacher = canBoRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Không tìm thấy giảng viên với ID: " + id));

        teacher.setTrangThai(status);
        canBoRepository.save(teacher);

        log.info("Teacher status updated successfully: {} - {}", teacher.getMaCanBo(), status);
    }

    private void mapTeacherRequestToEntity(TeacherRequest request, CanBo entity) {
        entity.setMaCanBo(request.getMaCanBo());
        entity.setTen(request.getTen());
        entity.setIdVaiTro(request.getIdVaiTro());
        entity.setIdPbmm(request.getIdPbmm());
        entity.setNgaySinh(request.getNgaySinh());
        entity.setNu(request.getNu());
        entity.setSdt(request.getSdt());
        entity.setEmail(request.getEmail());
        entity.setTrangThai(request.getTrangThai());
    }
}
