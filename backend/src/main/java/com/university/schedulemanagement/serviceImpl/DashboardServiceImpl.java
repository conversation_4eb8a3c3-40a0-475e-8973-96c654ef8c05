package com.university.schedulemanagement.serviceImpl;

import com.university.schedulemanagement.dto.response.DashboardResponse;
import com.university.schedulemanagement.dto.response.MonthlyStatsResponse;
import com.university.schedulemanagement.dto.response.SubjectStatsResponse;
import com.university.schedulemanagement.entity.*;
import com.university.schedulemanagement.exception.BadRequestException;
import com.university.schedulemanagement.repository.*;
import com.university.schedulemanagement.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * DashboardServiceImpl - Implementation của DashboardService
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class DashboardServiceImpl implements DashboardService {

    private final CanBoRepository canBoRepository;
    private final MonHocRepository monHocRepository;
    private final LopHocRepository lopHocRepository;
    private final LichGiangRepository lichGiangRepository;
    private final ThongKeGioGiangRepository thongKeGioGiangRepository;
    private final HocKyRepository hocKyRepository;
    private final PBMMRepository pbmmRepository;
    private final AuthService authService;

    @Override
    public DashboardResponse getDashboardStats() {
        log.info("Getting dashboard statistics");

        DashboardResponse response = new DashboardResponse();

        // Thống kê tổng quan
        response.setTotalTeachers(canBoRepository.count());
        response.setTotalSubjects(monHocRepository.count());
        response.setTotalClasses(lopHocRepository.count());
        response.setTotalSchedules(lichGiangRepository.count());

        // Tính tổng giờ giảng
        HocKy currentSemester = hocKyRepository.findByHienTaiTrue().orElse(null);
        if (currentSemester != null) {
            response.setCurrentSemesterId(currentSemester.getIdHocKy());
            response.setCurrentSemesterName(currentSemester.getTenHocKy());

            List<ThongKeGioGiang> currentStats = thongKeGioGiangRepository.findByIdHocKy(currentSemester.getIdHocKy());
            BigDecimal totalHours = currentStats.stream()
                    .map(stat -> stat.getTongGioQuyDoi() != null ? stat.getTongGioQuyDoi() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            response.setTotalTeachingHours(totalHours);
        }

        // Thống kê theo khoa
        response.setDepartmentStats(getDepartmentStatistics());

        // Thống kê theo tháng (12 tháng gần nhất)
        response.setMonthlyStats(getRecentMonthlyStats());

        return response;
    }

    @Override
    public DashboardResponse getDashboardStatsByDepartment(Long departmentId) {
        log.info("Getting dashboard statistics for department: {}", departmentId);

        if (!authService.canAccessDepartmentData(departmentId)) {
            throw new BadRequestException("Bạn không có quyền xem thống kê của khoa này");
        }

        DashboardResponse response = new DashboardResponse();

        // Thống kê theo khoa
        response.setTotalTeachers(canBoRepository.countByKhoa(departmentId));
        response.setTotalSubjects((long) monHocRepository.findByIdPbmm(departmentId).size());
        // TODO: Add class count by department

        HocKy currentSemester = hocKyRepository.findByHienTaiTrue().orElse(null);
        if (currentSemester != null) {
            response.setCurrentSemesterId(currentSemester.getIdHocKy());
            response.setCurrentSemesterName(currentSemester.getTenHocKy());

            List<ThongKeGioGiang> departmentStats = thongKeGioGiangRepository
                    .findByKhoaAndHocKy(departmentId, currentSemester.getIdHocKy());

            BigDecimal totalHours = departmentStats.stream()
                    .map(stat -> stat.getTongGioQuyDoi() != null ? stat.getTongGioQuyDoi() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            response.setTotalTeachingHours(totalHours);
        }

        return response;
    }

    @Override
    public List<MonthlyStatsResponse> getMonthlyTeachingHours(Long teacherId, int year) {
        // Implementation moved to TeachingHourService
        return Collections.emptyList(); // Placeholder
    }

    @Override
    public List<SubjectStatsResponse> getTopSubjectsByHours(Long semesterId, int limit) {
        log.info("Getting top {} subjects by hours for semester: {}", limit, semesterId);

        // TODO: Implement subject statistics
        return Collections.emptyList(); // Placeholder
    }

    private List<DashboardResponse.DepartmentStats> getDepartmentStatistics() {
        List<PBMM> departments = pbmmRepository.findAll();

        return departments.stream()
                .map(dept -> {
                    Long teacherCount = canBoRepository.countByKhoa(dept.getIdPbmm());
                    Long subjectCount = (long) monHocRepository.findByIdPbmm(dept.getIdPbmm()).size();

                    // Tính tổng giờ giảng của khoa
                    HocKy currentSemester = hocKyRepository.findByHienTaiTrue().orElse(null);
                    BigDecimal totalHours = BigDecimal.ZERO;

                    if (currentSemester != null) {
                        List<ThongKeGioGiang> deptStats = thongKeGioGiangRepository
                                .findByKhoaAndHocKy(dept.getIdPbmm(), currentSemester.getIdHocKy());

                        totalHours = deptStats.stream()
                                .map(stat -> stat.getTongGioQuyDoi() != null ? stat.getTongGioQuyDoi() : BigDecimal.ZERO)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    }

                    return new DashboardResponse.DepartmentStats(
                            dept.getIdPbmm(),
                            dept.getTenKhoa(),
                            teacherCount,
                            subjectCount,
                            totalHours
                    );
                })
                .collect(Collectors.toList());
    }

    private List<DashboardResponse.MonthlyStats> getRecentMonthlyStats() {
        // Placeholder implementation
        // TODO: Implement monthly statistics calculation
        return Collections.emptyList();
    }
}
