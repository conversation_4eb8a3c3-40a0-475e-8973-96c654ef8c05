package com.university.schedulemanagement.service;

import com.university.schedulemanagement.dto.request.AcademicYearRequest;
import com.university.schedulemanagement.dto.response.AcademicYearResponse;
import com.university.schedulemanagement.dto.response.PageResponse;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * AcademicYearService - Service quản lý năm học/niên khóa
 */
public interface AcademicYearService {

    /**
     * Lấy danh sách tất cả năm học với phân trang
     */
    PageResponse<AcademicYearResponse> getAllAcademicYears(Pageable pageable);

    /**
     * L<PERSON>y danh sách năm học đang hoạt động
     */
    List<AcademicYearResponse> getActiveAcademicYears();

    /**
     * Lấy thông tin năm học theo ID
     */
    AcademicYearResponse getAcademicYearById(Long id);

    /**
     * Tạ<PERSON> năm học mới
     */
    AcademicYearResponse createAcademicYear(AcademicYearRequest request);

    /**
     * Cập nhật thông tin năm học
     */
    AcademicYearResponse updateAcademicYear(Long id, AcademicYearRequest request);

    /**
     * Xóa năm học (chỉ khi chưa có dữ liệu liên quan)
     */
    void deleteAcademicYear(Long id);

    /**
     * Kích hoạt năm học (đặt làm năm học hiện tại)
     */
    void activateAcademicYear(Long id);

    /**
     * Lấy năm học hiện tại
     */
    AcademicYearResponse getCurrentAcademicYear();

    /**
     * Kiểm tra năm học có thể xóa không
     */
    boolean canDeleteAcademicYear(Long id);

    /**
     * Lấy thống kê của năm học
     */
    Object getAcademicYearStatistics(Long id);
}
