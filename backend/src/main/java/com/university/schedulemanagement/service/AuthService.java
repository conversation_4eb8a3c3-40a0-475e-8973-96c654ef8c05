package com.university.schedulemanagement.service;

import com.university.schedulemanagement.dto.request.*;
import com.university.schedulemanagement.dto.response.*;
import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;

/**
 * AuthService - Service xử lý xác thực
 */
public interface AuthService {

    LoginResponse login(LoginRequest request);

    void logout(String token);

    CanBo getCurrentUser();

    void changePassword(ChangePasswordRequest request);

    void resetPassword(String maCanBo);

    boolean validateToken(String token);

    // Role checking methods
    boolean isAdmin();

    boolean isTruongKhoa();

    boolean isGiangVien();

    // Permission checking methods
    boolean canAccessDepartmentData(Long departmentId);

    boolean canAccessTeacherData(Long teacherId);
}
