package com.university.schedulemanagement.service;

import com.university.schedulemanagement.dto.request.*;
import com.university.schedulemanagement.dto.response.PageResponse;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * MasterDataService - Service quản lý dữ liệu danh mục
 */
public interface MasterDataService {

    // ==================== QUẢN LÝ KHOA/PHÒNG BAN ====================
    
    /**
     * Lấy danh sách tất cả khoa
     */
    PageResponse<Object> getAllDepartments(Pageable pageable);

    /**
     * Tạo khoa mới
     */
    Object createDepartment(DepartmentRequest request);

    /**
     * Cập nhật thông tin khoa
     */
    Object updateDepartment(Long id, DepartmentRequest request);

    /**
     * Xóa khoa
     */
    void deleteDepartment(Long id);

    /**
     * <PERSON><PERSON><PERSON> thông tin khoa theo ID
     */
    Object getDepartmentById(Long id);

    // ==================== QUẢN LÝ MÔN HỌC ====================
    
    /**
     * Lấy danh sách môn học
     */
    PageResponse<Object> getAllSubjects(Long departmentId, Pageable pageable);

    /**
     * Tạo môn học mới
     */
    Object createSubject(SubjectRequest request);

    /**
     * Cập nhật môn học
     */
    Object updateSubject(Long id, SubjectRequest request);

    /**
     * Xóa môn học
     */
    void deleteSubject(Long id);

    /**
     * Lấy thông tin môn học theo ID
     */
    Object getSubjectById(Long id);

    // ==================== QUẢN LÝ LỚP HỌC ====================
    
    /**
     * Lấy danh sách lớp học
     */
    PageResponse<Object> getAllClasses(Long departmentId, Pageable pageable);

    /**
     * Tạo lớp học mới
     */
    Object createClass(ClassRequest request);

    /**
     * Cập nhật lớp học
     */
    Object updateClass(Long id, ClassRequest request);

    /**
     * Xóa lớp học
     */
    void deleteClass(Long id);

    /**
     * Lấy thông tin lớp học theo ID
     */
    Object getClassById(Long id);

    // ==================== QUẢN LÝ PHÒNG HỌC ====================
    
    /**
     * Lấy danh sách phòng học
     */
    PageResponse<Object> getAllRooms(Long campusId, Pageable pageable);

    /**
     * Tạo phòng học mới
     */
    Object createRoom(RoomRequest request);

    /**
     * Cập nhật phòng học
     */
    Object updateRoom(Long id, RoomRequest request);

    /**
     * Xóa phòng học
     */
    void deleteRoom(Long id);

    /**
     * Lấy thông tin phòng học theo ID
     */
    Object getRoomById(Long id);

    // ==================== QUẢN LÝ CƠ SỞ ====================
    
    /**
     * Lấy danh sách cơ sở
     */
    List<Object> getAllCampuses();

    /**
     * Tạo cơ sở mới
     */
    Object createCampus(CampusRequest request);

    /**
     * Cập nhật cơ sở
     */
    Object updateCampus(Long id, CampusRequest request);

    /**
     * Xóa cơ sở
     */
    void deleteCampus(Long id);

    // ==================== QUẢN LÝ GIẢNG VIÊN ====================
    
    /**
     * Lấy danh sách giảng viên
     */
    PageResponse<Object> getAllTeachers(Long departmentId, Pageable pageable);

    /**
     * Tạo giảng viên mới
     */
    Object createTeacher(TeacherRequest request);

    /**
     * Cập nhật giảng viên
     */
    Object updateTeacher(Long id, TeacherRequest request);

    /**
     * Xóa giảng viên
     */
    void deleteTeacher(Long id);

    // ==================== IMPORT DỮ LIỆU ====================
    
    /**
     * Import dữ liệu từ file Excel
     */
    Object importData(String dataType, MultipartFile file);

    /**
     * Export template Excel
     */
    byte[] exportTemplate(String dataType);

    /**
     * Đồng bộ dữ liệu từ hệ thống khác
     */
    Object syncDataFromExternalSystem(String systemType, Object config);
}
