package com.university.schedulemanagement.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * ExcelService - Service xử lý Import/Export Excel
 */
public interface ExcelService {

    /**
     * Import dữ liệu từ file Excel
     */
    <T> List<T> importFromExcel(MultipartFile file, Class<T> targetClass, String sheetName);

    /**
     * Export dữ liệu ra file Excel
     */
    <T> byte[] exportToExcel(List<T> data, Class<T> dataClass, String sheetName, String fileName);

    /**
     * Tạo template Excel cho import
     */
    byte[] createTemplate(String dataType, String fileName);

    /**
     * Validate dữ liệu import
     */
    <T> Map<String, Object> validateImportData(List<T> data, Class<T> dataClass);

    /**
     * Import dữ liệu khoa từ Excel
     */
    Map<String, Object> importDepartments(MultipartFile file);

    /**
     * Import dữ liệu môn học từ Excel
     */
    Map<String, Object> importSubjects(MultipartFile file);

    /**
     * Import dữ liệu lớp học từ Excel
     */
    Map<String, Object> importClasses(MultipartFile file);

    /**
     * Import dữ liệu phòng học từ Excel
     */
    Map<String, Object> importRooms(MultipartFile file);

    /**
     * Import dữ liệu giảng viên từ Excel
     */
    Map<String, Object> importTeachers(MultipartFile file);

    /**
     * Export template cho khoa
     */
    byte[] exportDepartmentTemplate();

    /**
     * Export template cho môn học
     */
    byte[] exportSubjectTemplate();

    /**
     * Export template cho lớp học
     */
    byte[] exportClassTemplate();

    /**
     * Export template cho phòng học
     */
    byte[] exportRoomTemplate();

    /**
     * Export template cho giảng viên
     */
    byte[] exportTeacherTemplate();
}
