package com.university.schedulemanagement.service;

import com.university.schedulemanagement.dto.request.*;
import com.university.schedulemanagement.dto.response.*;
import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;

/**
 * TeacherService - Service xử lý giảng viên
 */
public interface TeacherService {

    CanBo createTeacher(TeacherRequest request);

    CanBo updateTeacher(Long id, TeacherRequest request);

    void deleteTeacher(Long id);

    CanBo getTeacherById(Long id);

    Page<CanBo> getAllTeachers(Pageable pageable);

    Page<CanBo> searchTeachers(String keyword, Pageable pageable);

    List<CanBo> getTeachersByDepartment(Long departmentId);

    List<CanBo> getTeachersByRole(Long roleId);

    void updateTeacherStatus(Long id, Boolean status);
}
