package com.university.schedulemanagement.service;

import com.university.schedulemanagement.dto.request.SemesterRequest;
import com.university.schedulemanagement.dto.response.SemesterResponse;
import com.university.schedulemanagement.dto.response.PageResponse;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * SemesterService - Service quản lý học kỳ
 */
public interface SemesterService {

    /**
     * Lấy danh sách tất cả học kỳ với phân trang
     */
    PageResponse<SemesterResponse> getAllSemesters(Long academicYearId, Pageable pageable);

    /**
     * L<PERSON><PERSON> học kỳ hiện tại
     */
    SemesterResponse getCurrentSemester();

    /**
     * L<PERSON>y danh sách học kỳ theo năm học
     */
    List<SemesterResponse> getSemestersByAcademicYear(Long academicYearId);

    /**
     * <PERSON><PERSON><PERSON> thông tin học kỳ theo ID
     */
    SemesterResponse getSemesterById(Long id);

    /**
     * T<PERSON><PERSON> họ<PERSON> kỳ mới
     */
    SemesterResponse createSemester(SemesterRequest request);

    /**
     * Cập nhật thông tin học kỳ
     */
    SemesterResponse updateSemester(Long id, SemesterRequest request);

    /**
     * Xóa học kỳ (chỉ khi chưa có lịch giảng)
     */
    void deleteSemester(Long id);

    /**
     * Đặt học kỳ hiện tại
     */
    void setCurrentSemester(Long id);

    /**
     * Lấy thống kê của học kỳ
     */
    Object getSemesterStatistics(Long id);

    /**
     * Kiểm tra học kỳ có thể xóa không
     */
    boolean canDeleteSemester(Long id);

    /**
     * Lấy danh sách học kỳ đang hoạt động
     */
    List<SemesterResponse> getActiveSemesters();

    /**
     * Tự động cập nhật trạng thái học kỳ
     */
    void updateSemesterStatus();
}
