package com.university.schedulemanagement.service;

import com.university.schedulemanagement.dto.request.*;
import com.university.schedulemanagement.dto.response.*;
import com.university.schedulemanagement.entity.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;
/**
 * ExcelExportService - Service xuất Excel
 */
public interface ExcelExportService {

    /**
     * Xuất lịch giảng cá nhân
     */
    byte[] exportPersonalSchedule(Long teacherId, Long semesterId);

    /**
     * Xuất lịch giảng theo lớp
     */
    byte[] exportClassSchedule(Long classId, Long semesterId);

    /**
     * Xu<PERSON>t báo cáo tổng hợp giờ giảng
     */
    byte[] exportTeachingHoursReport(Long departmentId, Long semesterId);

    /**
     * Xuất danh sách giảng viên
     */
    byte[] exportTeacherList(Long departmentId);

    /**
     * Xu<PERSON>t danh sách môn học
     */
    byte[] exportSubjectList(Long departmentId);

    /**
     * Xuất lịch giảng toàn trường
     */
    byte[] exportFullSchedule(Long semesterId);
}
