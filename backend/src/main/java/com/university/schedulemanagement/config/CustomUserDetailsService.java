package com.university.schedulemanagement.config;

import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;

/**
 * CustomUserDetailsService - Service load user details
 */
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class CustomUserDetailsService implements org.springframework.security.core.userdetails.UserDetailsService {

    private final com.university.schedulemanagement.repository.CanBoRepository canBoRepository;

    @Override
    public org.springframework.security.core.userdetails.UserDetails loadUserByUsername(String maCanBo)
            throws org.springframework.security.core.userdetails.UsernameNotFoundException {

        com.university.schedulemanagement.entity.CanBo canBo = canBoRepository
                .findByMaCanBoAndTrangThaiTrue(maCanBo)
                .orElseThrow(() -> new org.springframework.security.core.userdetails.UsernameNotFoundException(
                        "<PERSON>hông tìm thấy người dùng với mã: " + maCanBo));

        return UserPrincipal.create(canBo);
    }

    public org.springframework.security.core.userdetails.UserDetails loadUserById(Long id) {
        com.university.schedulemanagement.entity.CanBo canBo = canBoRepository
                .findById(id)
                .orElseThrow(() -> new org.springframework.security.core.userdetails.UsernameNotFoundException(
                        "Không tìm thấy người dùng với ID: " + id));

        return UserPrincipal.create(canBo);
    }
}
