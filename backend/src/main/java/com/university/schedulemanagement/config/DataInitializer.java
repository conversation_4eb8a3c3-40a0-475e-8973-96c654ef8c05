package com.university.schedulemanagement.config;

import com.university.schedulemanagement.entity.*;
import com.university.schedulemanagement.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * DataInitializer - Khởi tạo dữ liệu mẫu cho hệ thống
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DataInitializer implements CommandLineRunner {

    private final VaiTroRepository vaiTroRepository;
    private final PBMMRepository pbmmRepository;
    private final CanBoRepository canBoRepository;
    private final NienKhoaRepository nienKhoaRepository;
    private final HocKyRepository hocKyRepository;
    private final BuoiHocRepository buoiHocRepository;
    private final CoSoRepository coSoRepository;
    private final PhongHocRepository phongHocRepository;
    private final HeDaoTaoRepository heDaoTaoRepository;
    private final LoaiMonHocRepository loaiMonHocRepository;
    private final HinhThucHocRepository hinhThucHocRepository;
    private final PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public void run(String... args) throws Exception {
        log.info("Starting data initialization...");

        try {
            initializeVaiTro();
            initializePBMM();
            initializeCanBo();
            initializeNienKhoa();
            initializeHocKy();
            initializeBuoiHoc();
            initializeCoSo();
            initializePhongHoc();
            initializeHeDaoTao();
            initializeLoaiMonHoc();
            initializeHinhThucHoc();

            log.info("Data initialization completed successfully!");
        } catch (Exception e) {
            log.error("Error during data initialization: {}", e.getMessage(), e);
        }
    }

    private void initializeVaiTro() {
        if (vaiTroRepository.count() == 0) {
            log.info("Initializing VaiTro data...");

            VaiTro admin = new VaiTro();
            admin.setTenVaiTro("Admin");
            admin.setMoTa("Quản trị hệ thống");
            vaiTroRepository.save(admin);

            VaiTro truongKhoa = new VaiTro();
            truongKhoa.setTenVaiTro("Trưởng khoa");
            truongKhoa.setMoTa("Trưởng khoa quản lý");
            vaiTroRepository.save(truongKhoa);

            VaiTro giangVien = new VaiTro();
            giangVien.setTenVaiTro("Giảng viên");
            giangVien.setMoTa("Giảng viên giảng dạy");
            vaiTroRepository.save(giangVien);

            log.info("VaiTro data initialized successfully");
        } else {
            log.info("VaiTro data already exists, skipping initialization");
        }
    }

    private void initializePBMM() {
        if (pbmmRepository.count() == 0) {
            log.info("Initializing PBMM data...");

            PBMM cntt = new PBMM();
            cntt.setMaKhoa("CNTT");
            cntt.setTenKhoa("Công nghệ thông tin");
            pbmmRepository.save(cntt);

            PBMM kt = new PBMM();
            kt.setMaKhoa("KT");
            kt.setTenKhoa("Kế toán");
            pbmmRepository.save(kt);

            PBMM qtkd = new PBMM();
            qtkd.setMaKhoa("QTKD");
            qtkd.setTenKhoa("Quản trị kinh doanh");
            pbmmRepository.save(qtkd);

            log.info("PBMM data initialized successfully");
        }
    }

    private void initializeCanBo() {
        log.info("Checking CanBo data...");

        // Kiểm tra xem đã có user admin chưa
        if (!canBoRepository.findByMaCanBoAndTrangThaiTrue("admin").isPresent()) {
            log.info("Creating admin user...");

            VaiTro adminRole = vaiTroRepository.findByTenVaiTro("Admin").orElse(null);
            PBMM cntt = pbmmRepository.findByMaKhoa("CNTT").orElse(null);

            if (adminRole != null && cntt != null) {
                CanBo admin = new CanBo();
                admin.setIdVaiTro(adminRole.getIdVaiTro());
                admin.setIdPbmm(cntt.getIdPbmm());
                admin.setMaCanBo("admin");
                admin.setMatKhau(passwordEncoder.encode("123456"));
                admin.setTen("Quản trị viên");
                admin.setEmail("<EMAIL>");
                admin.setSdt("0123456789");
                admin.setNu(false);
                admin.setTrangThai(true);
                canBoRepository.save(admin);
                log.info("Admin user created successfully");
            }
        } else {
            log.info("Admin user already exists");
        }

        // Kiểm tra xem đã có user truongkhoa chưa
        if (!canBoRepository.findByMaCanBoAndTrangThaiTrue("truongkhoa").isPresent()) {
            log.info("Creating truongkhoa user...");

            VaiTro truongKhoaRole = vaiTroRepository.findByTenVaiTro("Trưởng khoa").orElse(null);
            PBMM cntt = pbmmRepository.findByMaKhoa("CNTT").orElse(null);

            if (truongKhoaRole != null && cntt != null) {
                CanBo truongKhoa = new CanBo();
                truongKhoa.setIdVaiTro(truongKhoaRole.getIdVaiTro());
                truongKhoa.setIdPbmm(cntt.getIdPbmm());
                truongKhoa.setMaCanBo("truongkhoa");
                truongKhoa.setMatKhau(passwordEncoder.encode("123456"));
                truongKhoa.setTen("Trưởng khoa CNTT");
                truongKhoa.setEmail("<EMAIL>");
                truongKhoa.setSdt("0123456790");
                truongKhoa.setNu(false);
                truongKhoa.setTrangThai(true);
                canBoRepository.save(truongKhoa);
                log.info("Truongkhoa user created successfully");
            }
        } else {
            log.info("Truongkhoa user already exists");
        }

        // Kiểm tra xem đã có user giangvien chưa
        if (!canBoRepository.findByMaCanBoAndTrangThaiTrue("giangvien").isPresent()) {
            log.info("Creating giangvien user...");

            VaiTro giangVienRole = vaiTroRepository.findByTenVaiTro("Giảng viên").orElse(null);
            PBMM cntt = pbmmRepository.findByMaKhoa("CNTT").orElse(null);

            if (giangVienRole != null && cntt != null) {
                CanBo giangVien = new CanBo();
                giangVien.setIdVaiTro(giangVienRole.getIdVaiTro());
                giangVien.setIdPbmm(cntt.getIdPbmm());
                giangVien.setMaCanBo("giangvien");
                giangVien.setMatKhau(passwordEncoder.encode("123456"));
                giangVien.setTen("Nguyễn Văn A");
                giangVien.setEmail("<EMAIL>");
                giangVien.setSdt("0123456791");
                giangVien.setNu(false);
                giangVien.setTrangThai(true);
                canBoRepository.save(giangVien);
                log.info("Giangvien user created successfully");
            }
        } else {
            log.info("Giangvien user already exists");
        }

        log.info("CanBo data check completed");
    }

    private void initializeNienKhoa() {
        if (nienKhoaRepository.count() == 0) {
            log.info("Initializing NienKhoa data...");

            NienKhoa nienKhoa2024 = new NienKhoa();
            nienKhoa2024.setTenNienKhoa("2024-2025");
            nienKhoa2024.setNam(2024);
            nienKhoaRepository.save(nienKhoa2024);

            log.info("NienKhoa data initialized successfully");
        }
    }

    private void initializeHocKy() {
        if (hocKyRepository.count() == 0) {
            log.info("Initializing HocKy data...");

            NienKhoa nienKhoa = nienKhoaRepository.findAll().stream().findFirst().orElse(null);
            if (nienKhoa != null) {
                HocKy hocKy1 = new HocKy();
                hocKy1.setIdNienKhoa(nienKhoa.getIdNienKhoa());
                hocKy1.setTenHocKy("Học kỳ 1 (2024-2025)");
                hocKy1.setSoTuan(15);
                hocKy1.setNgayBatDau(LocalDate.of(2024, 9, 1));
                hocKy1.setNgayKetThuc(LocalDate.of(2024, 12, 31));
                hocKy1.setHienTai(true);
                hocKyRepository.save(hocKy1);
            }

            log.info("HocKy data initialized successfully");
        }
    }

    private void initializeBuoiHoc() {
        if (buoiHocRepository.count() == 0) {
            log.info("Initializing BuoiHoc data...");

            BuoiHoc sang = new BuoiHoc();
            sang.setTenBuoi("Sáng");
            sang.setGioBatDau(LocalTime.of(7, 0));
            sang.setGioKetThuc(LocalTime.of(11, 30));
            buoiHocRepository.save(sang);

            BuoiHoc chieu = new BuoiHoc();
            chieu.setTenBuoi("Chiều");
            chieu.setGioBatDau(LocalTime.of(13, 0));
            chieu.setGioKetThuc(LocalTime.of(17, 30));
            buoiHocRepository.save(chieu);

            BuoiHoc toi = new BuoiHoc();
            toi.setTenBuoi("Tối");
            toi.setGioBatDau(LocalTime.of(18, 0));
            toi.setGioKetThuc(LocalTime.of(21, 30));
            buoiHocRepository.save(toi);

            log.info("BuoiHoc data initialized successfully");
        }
    }

    private void initializeCoSo() {
        if (coSoRepository.count() == 0) {
            log.info("Initializing CoSo data...");

            CoSo cs1 = new CoSo();
            cs1.setMaCoSo("CS1");
            cs1.setTenCoSo("Cơ sở 1");
            cs1.setDiaChi("123 Đường ABC, Quận 1, TP.HCM");
            coSoRepository.save(cs1);

            CoSo cs2 = new CoSo();
            cs2.setMaCoSo("CS2");
            cs2.setTenCoSo("Cơ sở 2");
            cs2.setDiaChi("456 Đường XYZ, Quận 2, TP.HCM");
            coSoRepository.save(cs2);

            log.info("CoSo data initialized successfully");
        }
    }

    private void initializePhongHoc() {
        if (phongHocRepository.count() == 0) {
            log.info("Initializing PhongHoc data...");

            CoSo cs1 = coSoRepository.findByMaCoSo("CS1").orElse(null);
            if (cs1 != null) {
                for (int i = 101; i <= 110; i++) {
                    PhongHoc phong = new PhongHoc();
                    phong.setIdCoSo(cs1.getIdCoSo());
                    phong.setMaPhong("P" + i);
                    phong.setTenPhong("Phòng " + i);
                    phong.setLoaiPhong(i % 2 == 0 ? "TH" : "LT");
                    phong.setSucChua(i % 2 == 0 ? 30 : 50);
                    phong.setTrangThai(true);
                    phongHocRepository.save(phong);
                }
            }

            log.info("PhongHoc data initialized successfully");
        }
    }

    private void initializeHeDaoTao() {
        if (heDaoTaoRepository.count() == 0) {
            log.info("Initializing HeDaoTao data...");

            HeDaoTao chinh = new HeDaoTao();
            chinh.setTenHeDaoTao("Chính quy");
            chinh.setMoTa("Hệ đào tạo chính quy");
            heDaoTaoRepository.save(chinh);

            HeDaoTao lienThong = new HeDaoTao();
            lienThong.setTenHeDaoTao("Liên thông");
            lienThong.setMoTa("Hệ đào tạo liên thông");
            heDaoTaoRepository.save(lienThong);

            log.info("HeDaoTao data initialized successfully");
        }
    }

    private void initializeLoaiMonHoc() {
        if (loaiMonHocRepository.count() == 0) {
            log.info("Initializing LoaiMonHoc data...");

            LoaiMonHoc daiCuong = new LoaiMonHoc();
            daiCuong.setTenLoai("Đại cương");
            daiCuong.setMoTa("Môn học đại cương");
            loaiMonHocRepository.save(daiCuong);

            LoaiMonHoc coSo = new LoaiMonHoc();
            coSo.setTenLoai("Cơ sở");
            coSo.setMoTa("Môn học cơ sở");
            loaiMonHocRepository.save(coSo);

            LoaiMonHoc chuyenNganh = new LoaiMonHoc();
            chuyenNganh.setTenLoai("Chuyên ngành");
            chuyenNganh.setMoTa("Môn học chuyên ngành");
            loaiMonHocRepository.save(chuyenNganh);

            log.info("LoaiMonHoc data initialized successfully");
        }
    }

    private void initializeHinhThucHoc() {
        if (hinhThucHocRepository.count() == 0) {
            log.info("Initializing HinhThucHoc data...");

            HinhThucHoc lt = new HinhThucHoc();
            lt.setTenHinhThuc("LT");
            lt.setMoTa("Lý thuyết");
            hinhThucHocRepository.save(lt);

            HinhThucHoc th = new HinhThucHoc();
            th.setTenHinhThuc("TH");
            th.setMoTa("Thực hành");
            hinhThucHocRepository.save(th);

            log.info("HinhThucHoc data initialized successfully");
        }
    }
}
