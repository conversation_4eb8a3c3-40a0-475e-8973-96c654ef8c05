package com.university.schedulemanagement.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * SecurityConfig - Cấu hình bảo mật
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    private final JwtAuthenticationFilter jwtAuthenticationFilter;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public AuthenticationManager authenticationManager(
            AuthenticationConfiguration authConfig) throws Exception {
        return authConfig.getAuthenticationManager();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.cors().and().csrf().disable()
                .exceptionHandling()
                .authenticationEntryPoint(jwtAuthenticationEntryPoint)
                .and()
                .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .authorizeRequests(authz -> authz
                        // Public endpoints (without /api prefix because context-path is already /api)
                        .antMatchers("/auth/login").permitAll()
                        .antMatchers("/auth/reset-password").permitAll()
                        .antMatchers("/health").permitAll()
                        .antMatchers("/health/**").permitAll()

                        // Swagger endpoints (without /api prefix)
                        .antMatchers("/api-docs/**").permitAll()
                        .antMatchers("/swagger-ui/**").permitAll()
                        .antMatchers("/swagger-ui.html").permitAll()
                        .antMatchers("/swagger-resources/**").permitAll()
                        .antMatchers("/v3/api-docs/**").permitAll()
                        .antMatchers("/webjars/**").permitAll()

                        // Static resources
                        .antMatchers("/static/**").permitAll()

                        // Admin only endpoints (without /api prefix)
                        .antMatchers(HttpMethod.POST, "/teachers").hasRole("ADMIN")
                        .antMatchers(HttpMethod.PUT, "/teachers/**").hasRole("ADMIN")
                        .antMatchers(HttpMethod.DELETE, "/teachers/**").hasRole("ADMIN")
                        .antMatchers(HttpMethod.POST, "/subjects").hasAnyRole("ADMIN", "TRUONG_KHOA")
                        .antMatchers(HttpMethod.PUT, "/subjects/**").hasAnyRole("ADMIN", "TRUONG_KHOA")
                        .antMatchers(HttpMethod.DELETE, "/subjects/**").hasAnyRole("ADMIN", "TRUONG_KHOA")
                        .antMatchers(HttpMethod.POST, "/classes").hasAnyRole("ADMIN", "TRUONG_KHOA")
                        .antMatchers(HttpMethod.PUT, "/classes/**").hasAnyRole("ADMIN", "TRUONG_KHOA")
                        .antMatchers(HttpMethod.DELETE, "/classes/**").hasAnyRole("ADMIN", "TRUONG_KHOA")

                        // Schedule management (without /api prefix)
                        .antMatchers(HttpMethod.POST, "/schedules").hasAnyRole("ADMIN", "TRUONG_KHOA")
                        .antMatchers(HttpMethod.PUT, "/schedules/**").hasAnyRole("ADMIN", "TRUONG_KHOA")
                        .antMatchers(HttpMethod.DELETE, "/schedules/**").hasAnyRole("ADMIN", "TRUONG_KHOA")

                        // Teaching hours - Admins and Department heads can view all, teachers can view their own
                        .antMatchers(HttpMethod.GET, "/teaching-hours/personal").hasRole("GIANG_VIEN")
                        .antMatchers(HttpMethod.GET, "/teaching-hours/**").hasAnyRole("ADMIN", "TRUONG_KHOA", "GIANG_VIEN")

                        // Export functions (without /api prefix)
                        .antMatchers("/export/personal/**").hasRole("GIANG_VIEN")
                        .antMatchers("/export/**").hasAnyRole("ADMIN", "TRUONG_KHOA")

                        // All other requests need authentication
                        .anyRequest().authenticated()
                );

        http.addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
