package com.university.schedulemanagement.dto.request;

import javax.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * LoginRequest - DTO đăng nhập
 */
@Data
public class LoginRequest {

    @NotBlank(message = "Mã cán bộ không được để trống")
    private String maCanBo;

    @NotBlank(message = "Mật khẩu không được để trống")
    private String matKhau;
}
