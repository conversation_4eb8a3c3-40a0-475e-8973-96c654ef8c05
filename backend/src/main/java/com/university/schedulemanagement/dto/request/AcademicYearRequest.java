package com.university.schedulemanagement.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * AcademicYearRequest - DTO cho request tạo/cập nhật năm học
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AcademicYearRequest {

    @NotBlank(message = "Tên niên khóa không được để trống")
    private String tenNienKhoa;

    @NotNull(message = "Năm không được để trống")
    @Min(value = 2020, message = "Năm phải từ 2020 trở lên")
    @Max(value = 2050, message = "Năm không được quá 2050")
    private Integer nam;

    private Long idThongTu;

    private String moTa;

    private Boolean trangThai = true;
}
