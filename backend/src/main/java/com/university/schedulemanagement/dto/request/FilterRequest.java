package com.university.schedulemanagement.dto.request;

import javax.validation.constraints.NotNull;
import lombok.Data;
import java.time.LocalDate;

/**
 * FilterRequest - DTO lọc dữ liệu chung
 */
@Data
public class FilterRequest {

    private String keyword;

    private Long departmentId;

    private Long semesterId;

    private Long educationLevelId;

    private Long majorId;

    private Long classId;

    private Long teacherId;

    private LocalDate fromDate;

    private LocalDate toDate;

    private Boolean status;

    private String sortBy = "id";

    private String sortDirection = "ASC";

    private Integer page = 0;

    private Integer size = 10;
}
