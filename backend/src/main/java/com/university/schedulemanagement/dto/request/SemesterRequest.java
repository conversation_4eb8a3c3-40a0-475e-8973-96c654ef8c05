package com.university.schedulemanagement.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import java.time.LocalDate;

/**
 * SemesterRequest - DTO cho request tạo/cập nhật học kỳ
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SemesterRequest {

    @NotNull(message = "ID niên khóa không được để trống")
    private Long idNienKhoa;

    @NotBlank(message = "Tên học kỳ không được để trống")
    private String tenHocKy;

    @Min(value = 1, message = "Số tuần phải lớn hơn 0")
    @Max(value = 52, message = "Số tuần không được quá 52")
    private Integer soTuan;

    @NotNull(message = "<PERSON><PERSON><PERSON> bắt đầu không được để trống")
    private LocalDate ngayBatDau;

    @NotNull(message = "<PERSON><PERSON><PERSON> kết thúc không được để trống")
    private LocalDate ngayKetThuc;

    private Boolean hienTai = false;

    private String moTa;

    private Boolean trangThai = true;
}
