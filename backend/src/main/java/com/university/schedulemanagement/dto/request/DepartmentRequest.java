package com.university.schedulemanagement.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * DepartmentRequest - DTO cho request tạo/cập nhật khoa
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DepartmentRequest {

    @NotBlank(message = "Mã khoa không được để trống")
    @Size(max = 10, message = "Mã khoa không được quá 10 ký tự")
    private String maKhoa;

    @NotBlank(message = "Tên khoa không được để trống")
    @Size(max = 250, message = "Tên khoa không được quá 250 ký tự")
    private String tenKhoa;

    @Size(max = 500, message = "<PERSON>ô tả không được quá 500 ký tự")
    private String moTa;

    private String diaChi;

    private String soDienThoai;

    private String email;

    private Boolean trangThai = true;
}
