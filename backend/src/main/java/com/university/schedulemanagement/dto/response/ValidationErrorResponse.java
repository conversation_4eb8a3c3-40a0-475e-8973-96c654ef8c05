package com.university.schedulemanagement.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * ValidationErrorResponse - DTO response lỗi validation
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ValidationErrorResponse {

    private String message;

    private Map<String, List<String>> fieldErrors;

    private List<String> globalErrors;
}
