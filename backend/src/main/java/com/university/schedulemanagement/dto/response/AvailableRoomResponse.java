package com.university.schedulemanagement.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalTime;

/**
 * AvailableRoomResponse - DTO response phòng học khả dụng
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AvailableRoomResponse {

    private Long roomId;

    private String maPhong;

    private String tenPhong;

    private String loaiPhong;

    private Integer sucChua;

    private String tenCoSo;

    private String maCoSo;

    private boolean isAvailable;

    private String description;
}
