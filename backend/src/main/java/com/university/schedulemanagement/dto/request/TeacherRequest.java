package com.university.schedulemanagement.dto.request;

import javax.validation.constraints.*;
import lombok.Data;
import java.time.LocalDate;

/**
 * TeacherRequest - DTO tạo/cập nhật giảng viên
 */
@Data
public class TeacherRequest {

    @NotBlank(message = "Mã cán bộ không được để trống")
    private String maCanBo;

    @NotBlank(message = "Tên không được để trống")
    private String ten;

    @NotNull(message = "Phải chọn vai trò")
    private Long idVaiTro;

    @NotNull(message = "Phải chọn khoa")
    private Long idPbmm;

    private LocalDate ngaySinh;

    private Boolean nu = false;

    @Pattern(regexp = "^[0-9]{10,11}$", message = "Số điện thoại không hợp lệ")
    private String sdt;

    @Email(message = "Email không hợp lệ")
    private String email;

    private Boolean trangThai = true;
}
