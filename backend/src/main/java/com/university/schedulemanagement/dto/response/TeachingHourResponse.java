package com.university.schedulemanagement.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * TeachingHourResponse - DTO response tổng hợp giờ giảng
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TeachingHourResponse {

    private Long teacherId;

    private String maCanBo;

    private String tenCanBo;

    private String tenKhoa;

    private Long semesterId;

    private String tenHocKy;

    private BigDecimal tongGioLt;

    private BigDecimal tongGioTh;

    private BigDecimal tongGioTong;

    private BigDecimal tongGioQuyDoi;

    private String tyLePhanBo;

    private LocalDateTime ngayTinh;

    private List<ScheduleResponse> lichGiangList;
}
