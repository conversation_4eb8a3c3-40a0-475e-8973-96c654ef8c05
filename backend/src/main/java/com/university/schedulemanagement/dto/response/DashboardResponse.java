package com.university.schedulemanagement.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * DashboardResponse - DTO response dashboard
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DashboardResponse {

    private Long totalTeachers;

    private Long totalSubjects;

    private Long totalClasses;

    private Long totalSchedules;

    private BigDecimal totalTeachingHours;

    private Long currentSemesterId;

    private String currentSemesterName;

    private List<DepartmentStats> departmentStats;

    private List<MonthlyStats> monthlyStats;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor

    public static class DepartmentStats {
        private Long departmentId;
        private String departmentName;
        private Long totalTeachers;
        private Long totalSubjects;
        private BigDecimal totalTeachingHours;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MonthlyStats {
        private String monthName;
        private Long totalSchedules;
        private BigDecimal totalTeachingHours;
    }
}
