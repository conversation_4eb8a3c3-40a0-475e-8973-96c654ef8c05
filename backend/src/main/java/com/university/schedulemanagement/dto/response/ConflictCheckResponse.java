package com.university.schedulemanagement.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * ConflictCheckResponse - DTO response kiểm tra xung đột lịch
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConflictCheckResponse {

    private boolean hasConflict;

    private String message;

    private String conflictType;

    private List<ConflictDetail> conflicts;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConflictDetail {
        private String type; // TEACHER, ROOM, CLASS
        private String description;
        private Long scheduleId;
        private String scheduleName;
    }
}
